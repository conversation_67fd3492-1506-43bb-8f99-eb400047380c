[{"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "196.083µs", "timestamp": "2025-06-19T17:26:47.799075+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123456789", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "236.875µs", "timestamp": "2025-06-19T17:26:47.900081+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "199.375µs", "timestamp": "2025-06-19T17:26:48.000062+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123456", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "308.792µs", "timestamp": "2025-06-19T17:26:48.100185+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "209.5µs", "timestamp": "2025-06-19T17:26:48.200067+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000699292s", "timestamp": "2025-06-19T17:26:49.300526+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.0012035s", "timestamp": "2025-06-19T17:26:49.401033+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "12345678", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "181.375µs", "timestamp": "2025-06-19T17:26:49.401246+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "95.083µs", "timestamp": "2025-06-19T17:26:49.401351+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "69.833µs", "timestamp": "2025-06-19T17:26:49.401428+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "63.917µs", "timestamp": "2025-06-19T17:26:49.401498+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001088208s", "timestamp": "2025-06-19T17:26:49.799866+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123456", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0011285s", "timestamp": "2025-06-19T17:26:49.799913+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001162959s", "timestamp": "2025-06-19T17:26:50.402642+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "12345", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "175.084µs", "timestamp": "2025-06-19T17:26:50.402837+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "95.292µs", "timestamp": "2025-06-19T17:26:50.402943+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123456789", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "80.208µs", "timestamp": "2025-06-19T17:26:50.403032+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.042µs", "timestamp": "2025-06-19T17:26:50.403117+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001161292s", "timestamp": "2025-06-19T17:26:50.801031+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123456", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001129833s", "timestamp": "2025-06-19T17:26:51.301633+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000228167s", "timestamp": "2025-06-19T17:26:51.403333+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123456789", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001042208s", "timestamp": "2025-06-19T17:26:51.800953+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001112958s", "timestamp": "2025-06-19T17:26:51.800978+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000084541s", "timestamp": "2025-06-19T17:26:51.801108+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234567890", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "210.459µs", "timestamp": "2025-06-19T17:26:51.801183+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "212µs", "timestamp": "2025-06-19T17:26:51.801211+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "12345678", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "118.459µs", "timestamp": "2025-06-19T17:26:51.801236+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "110.458µs", "timestamp": "2025-06-19T17:26:51.801304+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001139541s", "timestamp": "2025-06-19T17:26:52.802333+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001167s", "timestamp": "2025-06-19T17:26:52.903155+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234567", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "210.458µs", "timestamp": "2025-06-19T17:26:52.9034+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "105.375µs", "timestamp": "2025-06-19T17:26:52.903512+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "12345", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "101.542µs", "timestamp": "2025-06-19T17:26:52.903622+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "100.75µs", "timestamp": "2025-06-19T17:26:52.90373+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123456789", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001118083s", "timestamp": "2025-06-19T17:26:53.302717+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "12345678", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001096125s", "timestamp": "2025-06-19T17:26:53.40439+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001083375s", "timestamp": "2025-06-19T17:26:53.904791+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "12345678", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001101917s", "timestamp": "2025-06-19T17:26:54.003054+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "208.708µs", "timestamp": "2025-06-19T17:26:54.003284+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "117.334µs", "timestamp": "2025-06-19T17:26:54.003408+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234567890", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "101.5µs", "timestamp": "2025-06-19T17:26:54.003519+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "89.583µs", "timestamp": "2025-06-19T17:26:54.003615+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001143667s", "timestamp": "2025-06-19T17:26:54.303851+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "12345", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001084417s", "timestamp": "2025-06-19T17:26:54.803382+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000213792s", "timestamp": "2025-06-19T17:26:55.005172+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001133417s", "timestamp": "2025-06-19T17:26:55.30497+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "000000", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "191.958µs", "timestamp": "2025-06-19T17:26:55.305185+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "123.792µs", "timestamp": "2025-06-19T17:26:55.305341+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234567", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "111.208µs", "timestamp": "2025-06-19T17:26:55.305463+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "99.334µs", "timestamp": "2025-06-19T17:26:55.305569+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "12345", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001056375s", "timestamp": "2025-06-19T17:26:55.405431+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234567890", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001067625s", "timestamp": "2025-06-19T17:26:55.905838+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001162375s", "timestamp": "2025-06-19T17:26:56.306711+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001094125s", "timestamp": "2025-06-19T17:26:56.406509+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "178.875µs", "timestamp": "2025-06-19T17:26:56.406703+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "128.333µs", "timestamp": "2025-06-19T17:26:56.406838+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "99.917µs", "timestamp": "2025-06-19T17:26:56.406948+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "81.708µs", "timestamp": "2025-06-19T17:26:56.407035+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234567890", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0010885s", "timestamp": "2025-06-19T17:26:56.804434+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234567", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001085959s", "timestamp": "2025-06-19T17:26:57.006233+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001150834s", "timestamp": "2025-06-19T17:26:57.408163+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001173375s", "timestamp": "2025-06-19T17:26:57.805596+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "199.417µs", "timestamp": "2025-06-19T17:26:57.805854+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "120.708µs", "timestamp": "2025-06-19T17:26:57.805986+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "000000", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "92.792µs", "timestamp": "2025-06-19T17:26:57.806086+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "83.416µs", "timestamp": "2025-06-19T17:26:57.806177+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234567", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001074458s", "timestamp": "2025-06-19T17:26:57.906869+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001111167s", "timestamp": "2025-06-19T17:26:58.307794+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.0002955s", "timestamp": "2025-06-19T17:26:58.806449+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00116275s", "timestamp": "2025-06-19T17:26:58.908021+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "208.375µs", "timestamp": "2025-06-19T17:26:58.908248+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "125.417µs", "timestamp": "2025-06-19T17:26:58.908389+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "100.916µs", "timestamp": "2025-06-19T17:26:58.908516+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "64.833µs", "timestamp": "2025-06-19T17:26:58.908586+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001074375s", "timestamp": "2025-06-19T17:26:59.007265+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "000000", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001085292s", "timestamp": "2025-06-19T17:26:59.409215+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001107375s", "timestamp": "2025-06-19T17:26:59.90968+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001115125s", "timestamp": "2025-06-19T17:27:00.00926+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "170.542µs", "timestamp": "2025-06-19T17:27:00.009444+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "113.625µs", "timestamp": "2025-06-19T17:27:00.009565+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "126.875µs", "timestamp": "2025-06-19T17:27:00.009701+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "82.917µs", "timestamp": "2025-06-19T17:27:00.009789+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "000000", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001067625s", "timestamp": "2025-06-19T17:27:00.308829+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00106875s", "timestamp": "2025-06-19T17:27:00.807501+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001180208s", "timestamp": "2025-06-19T17:27:01.010947+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001132833s", "timestamp": "2025-06-19T17:27:01.30995+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "pass", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "179.291µs", "timestamp": "2025-06-19T17:27:01.310149+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "95.792µs", "timestamp": "2025-06-19T17:27:01.310255+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "87.958µs", "timestamp": "2025-06-19T17:27:01.310353+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "64.916µs", "timestamp": "2025-06-19T17:27:01.310424+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000521791s", "timestamp": "2025-06-19T17:27:01.409694+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001090375s", "timestamp": "2025-06-19T17:27:01.910734+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00018125s", "timestamp": "2025-06-19T17:27:02.310585+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001225375s", "timestamp": "2025-06-19T17:27:02.411241+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "280.417µs", "timestamp": "2025-06-19T17:27:02.411563+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "129.209µs", "timestamp": "2025-06-19T17:27:02.411704+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "89.834µs", "timestamp": "2025-06-19T17:27:02.411801+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "101.958µs", "timestamp": "2025-06-19T17:27:02.411913+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116541s", "timestamp": "2025-06-19T17:27:02.808593+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0011185s", "timestamp": "2025-06-19T17:27:03.012043+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00105s", "timestamp": "2025-06-19T17:27:03.412941+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001133042s", "timestamp": "2025-06-19T17:27:03.809722+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "guest", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "183.5µs", "timestamp": "2025-06-19T17:27:03.809929+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "98.75µs", "timestamp": "2025-06-19T17:27:03.810037+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "pass", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.334µs", "timestamp": "2025-06-19T17:27:03.810124+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "84.584µs", "timestamp": "2025-06-19T17:27:03.810218+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001117667s", "timestamp": "2025-06-19T17:27:03.911817+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000360542s", "timestamp": "2025-06-19T17:27:04.31091+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001265333s", "timestamp": "2025-06-19T17:27:04.811466+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001180209s", "timestamp": "2025-06-19T17:27:04.912999+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "194.542µs", "timestamp": "2025-06-19T17:27:04.913217+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "87.625µs", "timestamp": "2025-06-19T17:27:04.913313+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "86.5µs", "timestamp": "2025-06-19T17:27:04.913408+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "75.75µs", "timestamp": "2025-06-19T17:27:04.913494+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001066709s", "timestamp": "2025-06-19T17:27:05.013074+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "pass", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001125167s", "timestamp": "2025-06-19T17:27:05.414046+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001137458s", "timestamp": "2025-06-19T17:27:05.91461+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001102708s", "timestamp": "2025-06-19T17:27:06.014167+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "149.208µs", "timestamp": "2025-06-19T17:27:06.014338+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "91.125µs", "timestamp": "2025-06-19T17:27:06.014438+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "guest", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "64.875µs", "timestamp": "2025-06-19T17:27:06.014509+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "80.25µs", "timestamp": "2025-06-19T17:27:06.014597+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "pass", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001102709s", "timestamp": "2025-06-19T17:27:06.312004+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000761791s", "timestamp": "2025-06-19T17:27:06.812208+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001129791s", "timestamp": "2025-06-19T17:27:07.015755+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001133583s", "timestamp": "2025-06-19T17:27:07.313132+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "203.416µs", "timestamp": "2025-06-19T17:27:07.313382+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.542µs", "timestamp": "2025-06-19T17:27:07.313514+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "119.625µs", "timestamp": "2025-06-19T17:27:07.313643+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "96.541µs", "timestamp": "2025-06-19T17:27:07.313747+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001098416s", "timestamp": "2025-06-19T17:27:07.41511+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "guest", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001143875s", "timestamp": "2025-06-19T17:27:07.91572+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00119175s", "timestamp": "2025-06-19T17:27:08.314918+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001161667s", "timestamp": "2025-06-19T17:27:08.416258+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123qwe", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "208µs", "timestamp": "2025-06-19T17:27:08.416519+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.958µs", "timestamp": "2025-06-19T17:27:08.416662+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "104.458µs", "timestamp": "2025-06-19T17:27:08.416774+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "108.5µs", "timestamp": "2025-06-19T17:27:08.41689+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "guest", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001113459s", "timestamp": "2025-06-19T17:27:08.813287+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001075708s", "timestamp": "2025-06-19T17:27:09.016792+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001142458s", "timestamp": "2025-06-19T17:27:09.41801+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000974625s", "timestamp": "2025-06-19T17:27:09.81426+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "qwerty", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "245.25µs", "timestamp": "2025-06-19T17:27:09.814533+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "108.708µs", "timestamp": "2025-06-19T17:27:09.814649+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "104.708µs", "timestamp": "2025-06-19T17:27:09.814778+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "81.458µs", "timestamp": "2025-06-19T17:27:09.814868+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001091291s", "timestamp": "2025-06-19T17:27:09.916799+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "1234", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001086416s", "timestamp": "2025-06-19T17:27:10.315976+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001091083s", "timestamp": "2025-06-19T17:27:10.815938+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001129666s", "timestamp": "2025-06-19T17:27:10.917915+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "abc123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "197.417µs", "timestamp": "2025-06-19T17:27:10.918131+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "140.875µs", "timestamp": "2025-06-19T17:27:10.918281+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123qwe", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "112.958µs", "timestamp": "2025-06-19T17:27:10.918405+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "108.792µs", "timestamp": "2025-06-19T17:27:10.918522+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "1234", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001084375s", "timestamp": "2025-06-19T17:27:11.017869+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001102791s", "timestamp": "2025-06-19T17:27:11.419089+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001119167s", "timestamp": "2025-06-19T17:27:11.919619+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001165542s", "timestamp": "2025-06-19T17:27:12.019116+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "Password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "194.916µs", "timestamp": "2025-06-19T17:27:12.019333+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "115.583µs", "timestamp": "2025-06-19T17:27:12.019455+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "qwerty", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "109.333µs", "timestamp": "2025-06-19T17:27:12.019573+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "83.459µs", "timestamp": "2025-06-19T17:27:12.019662+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001090625s", "timestamp": "2025-06-19T17:27:12.317024+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123qwe", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001073625s", "timestamp": "2025-06-19T17:27:12.816976+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001128416s", "timestamp": "2025-06-19T17:27:13.020769+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001126375s", "timestamp": "2025-06-19T17:27:13.318138+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "welcome", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "239.209µs", "timestamp": "2025-06-19T17:27:13.318397+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "Password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "119.417µs", "timestamp": "2025-06-19T17:27:13.318526+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "abc123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "121.041µs", "timestamp": "2025-06-19T17:27:13.318669+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "102.958µs", "timestamp": "2025-06-19T17:27:13.318781+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123qwe", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001105666s", "timestamp": "2025-06-19T17:27:13.420156+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "qwerty", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001101333s", "timestamp": "2025-06-19T17:27:13.920684+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001119708s", "timestamp": "2025-06-19T17:27:14.31988+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001168208s", "timestamp": "2025-06-19T17:27:14.421315+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "login", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "213.709µs", "timestamp": "2025-06-19T17:27:14.421559+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "welcome", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "121.458µs", "timestamp": "2025-06-19T17:27:14.421697+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "Password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "98.292µs", "timestamp": "2025-06-19T17:27:14.421809+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "Password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "69µs", "timestamp": "2025-06-19T17:27:14.421885+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "qwerty", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001071834s", "timestamp": "2025-06-19T17:27:14.818008+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "abc123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001094209s", "timestamp": "2025-06-19T17:27:15.021824+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "Password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00109525s", "timestamp": "2025-06-19T17:27:15.422962+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "Password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00114075s", "timestamp": "2025-06-19T17:27:15.81914+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "changeme", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "185.875µs", "timestamp": "2025-06-19T17:27:15.819341+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "login", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "141.333µs", "timestamp": "2025-06-19T17:27:15.819491+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "welcome", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "113.25µs", "timestamp": "2025-06-19T17:27:15.819613+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "welcome", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "96.833µs", "timestamp": "2025-06-19T17:27:15.819717+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "abc123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000153125s", "timestamp": "2025-06-19T17:27:15.920798+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "Password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000782083s", "timestamp": "2025-06-19T17:27:16.320624+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "welcome", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001143458s", "timestamp": "2025-06-19T17:27:16.820839+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "welcome", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001140417s", "timestamp": "2025-06-19T17:27:16.921929+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "secret", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "190.5µs", "timestamp": "2025-06-19T17:27:16.922148+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "changeme", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "108.459µs", "timestamp": "2025-06-19T17:27:16.922268+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "login", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "93.25µs", "timestamp": "2025-06-19T17:27:16.922371+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "login", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.708µs", "timestamp": "2025-06-19T17:27:16.922458+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "Password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001075917s", "timestamp": "2025-06-19T17:27:17.022887+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "welcome", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001126958s", "timestamp": "2025-06-19T17:27:17.424051+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "login", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00117025s", "timestamp": "2025-06-19T17:27:17.923614+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "login", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001172625s", "timestamp": "2025-06-19T17:27:18.02405+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "administrator", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "194.167µs", "timestamp": "2025-06-19T17:27:18.02428+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "secret", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "114.959µs", "timestamp": "2025-06-19T17:27:18.024403+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "changeme", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "88.542µs", "timestamp": "2025-06-19T17:27:18.024502+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "changeme", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "65.25µs", "timestamp": "2025-06-19T17:27:18.024576+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "welcome", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001113041s", "timestamp": "2025-06-19T17:27:18.321703+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "login", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001220542s", "timestamp": "2025-06-19T17:27:18.822035+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "changeme", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00118925s", "timestamp": "2025-06-19T17:27:19.025743+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "changeme", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001155292s", "timestamp": "2025-06-19T17:27:19.322868+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "letmein", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "163.417µs", "timestamp": "2025-06-19T17:27:19.323053+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "administrator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "85.709µs", "timestamp": "2025-06-19T17:27:19.323148+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "secret", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "78.458µs", "timestamp": "2025-06-19T17:27:19.323234+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "secret", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "93.291µs", "timestamp": "2025-06-19T17:27:19.323338+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "login", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000973167s", "timestamp": "2025-06-19T17:27:19.424993+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "changeme", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099958s", "timestamp": "2025-06-19T17:27:19.9247+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "secret", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001168375s", "timestamp": "2025-06-19T17:27:20.324485+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "secret", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001167167s", "timestamp": "2025-06-19T17:27:20.42615+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "dragon", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "238.708µs", "timestamp": "2025-06-19T17:27:20.426437+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "letmein", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "95.958µs", "timestamp": "2025-06-19T17:27:20.426548+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "administrator", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "81.708µs", "timestamp": "2025-06-19T17:27:20.426642+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "administrator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "75.917µs", "timestamp": "2025-06-19T17:27:20.426734+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "changeme", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001111833s", "timestamp": "2025-06-19T17:27:20.823158+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "secret", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001108125s", "timestamp": "2025-06-19T17:27:21.026821+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "administrator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001136875s", "timestamp": "2025-06-19T17:27:21.427849+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "administrator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001165416s", "timestamp": "2025-06-19T17:27:21.824319+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "master", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "214.542µs", "timestamp": "2025-06-19T17:27:21.824556+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "dragon", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "118.042µs", "timestamp": "2025-06-19T17:27:21.824685+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "letmein", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "135.292µs", "timestamp": "2025-06-19T17:27:21.824828+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "letmein", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "102.875µs", "timestamp": "2025-06-19T17:27:21.824939+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "secret", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099583s", "timestamp": "2025-06-19T17:27:21.925762+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "administrator", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001104709s", "timestamp": "2025-06-19T17:27:22.325558+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "letmein", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00109775s", "timestamp": "2025-06-19T17:27:22.826016+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "letmein", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001133875s", "timestamp": "2025-06-19T17:27:22.926885+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "hello", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "191.25µs", "timestamp": "2025-06-19T17:27:22.927094+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "master", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "134.834µs", "timestamp": "2025-06-19T17:27:22.927239+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "dragon", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "96.583µs", "timestamp": "2025-06-19T17:27:22.927349+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "dragon", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "91.875µs", "timestamp": "2025-06-19T17:27:22.927447+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "administrator", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001117917s", "timestamp": "2025-06-19T17:27:23.027905+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "letmein", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099334s", "timestamp": "2025-06-19T17:27:23.428912+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "dragon", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001090875s", "timestamp": "2025-06-19T17:27:23.928518+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "dragon", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00116775s", "timestamp": "2025-06-19T17:27:24.029061+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "freedom", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "200.042µs", "timestamp": "2025-06-19T17:27:24.029283+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "hello", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "125.416µs", "timestamp": "2025-06-19T17:27:24.02942+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "master", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "98.75µs", "timestamp": "2025-06-19T17:27:24.029531+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "master", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "94.417µs", "timestamp": "2025-06-19T17:27:24.029637+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "letmein", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00111025s", "timestamp": "2025-06-19T17:27:24.32663+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "dragon", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099792s", "timestamp": "2025-06-19T17:27:24.827083+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "master", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000188209s", "timestamp": "2025-06-19T17:27:25.029805+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "master", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001155416s", "timestamp": "2025-06-19T17:27:25.327779+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "whatever", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "226.667µs", "timestamp": "2025-06-19T17:27:25.328026+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "freedom", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "137.083µs", "timestamp": "2025-06-19T17:27:25.328171+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "hello", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "113.583µs", "timestamp": "2025-06-19T17:27:25.328295+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "hello", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "102.875µs", "timestamp": "2025-06-19T17:27:25.328404+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "dragon", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001106292s", "timestamp": "2025-06-19T17:27:25.429978+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "master", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001064334s", "timestamp": "2025-06-19T17:27:25.929563+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "hello", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001113s", "timestamp": "2025-06-19T17:27:26.329504+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "hello", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001123166s", "timestamp": "2025-06-19T17:27:26.431088+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "qazwsx", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "188.833µs", "timestamp": "2025-06-19T17:27:26.431304+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "whatever", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "120.334µs", "timestamp": "2025-06-19T17:27:26.431431+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "freedom", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "152.167µs", "timestamp": "2025-06-19T17:27:26.431591+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "freedom", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "118.917µs", "timestamp": "2025-06-19T17:27:26.431719+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "master", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001074208s", "timestamp": "2025-06-19T17:27:26.82812+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "hello", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001144542s", "timestamp": "2025-06-19T17:27:27.030923+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "freedom", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001177666s", "timestamp": "2025-06-19T17:27:27.432874+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "freedom", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001166792s", "timestamp": "2025-06-19T17:27:27.829277+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "trustno1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "190.917µs", "timestamp": "2025-06-19T17:27:27.829509+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "qazwsx", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.25µs", "timestamp": "2025-06-19T17:27:27.829659+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "whatever", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "115.417µs", "timestamp": "2025-06-19T17:27:27.829786+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "whatever", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "68.375µs", "timestamp": "2025-06-19T17:27:27.829875+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "hello", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001127042s", "timestamp": "2025-06-19T17:27:27.930648+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "freedom", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001108583s", "timestamp": "2025-06-19T17:27:28.330576+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "whatever", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001122417s", "timestamp": "2025-06-19T17:27:28.830977+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "whatever", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001143125s", "timestamp": "2025-06-19T17:27:28.93178+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "oracle", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "185.833µs", "timestamp": "2025-06-19T17:27:28.931984+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "trustno1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "89.292µs", "timestamp": "2025-06-19T17:27:28.932081+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "qazwsx", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "92.209µs", "timestamp": "2025-06-19T17:27:28.932182+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "qazwsx", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "95.834µs", "timestamp": "2025-06-19T17:27:28.932285+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "freedom", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001104458s", "timestamp": "2025-06-19T17:27:29.032+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "whatever", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0010775s", "timestamp": "2025-06-19T17:27:29.433914+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "qazwsx", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00113s", "timestamp": "2025-06-19T17:27:29.933393+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "qazwsx", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001120167s", "timestamp": "2025-06-19T17:27:30.033107+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "postgres", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "197.25µs", "timestamp": "2025-06-19T17:27:30.033336+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "oracle", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "154.583µs", "timestamp": "2025-06-19T17:27:30.033503+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "trustno1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "126.542µs", "timestamp": "2025-06-19T17:27:30.033639+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "trustno1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "103.75µs", "timestamp": "2025-06-19T17:27:30.033752+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "whatever", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001097041s", "timestamp": "2025-06-19T17:27:30.331635+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "qazwsx", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001098417s", "timestamp": "2025-06-19T17:27:30.832053+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "trustno1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001126916s", "timestamp": "2025-06-19T17:27:31.034857+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "trustno1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001135833s", "timestamp": "2025-06-19T17:27:31.332762+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "mysql", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "172.708µs", "timestamp": "2025-06-19T17:27:31.333006+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "postgres", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "110.958µs", "timestamp": "2025-06-19T17:27:31.333153+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "oracle", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "106.791µs", "timestamp": "2025-06-19T17:27:31.333269+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "oracle", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "77.458µs", "timestamp": "2025-06-19T17:27:31.333356+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "qazwsx", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001069791s", "timestamp": "2025-06-19T17:27:31.434942+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "trustno1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099333s", "timestamp": "2025-06-19T17:27:31.934456+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "oracle", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001124666s", "timestamp": "2025-06-19T17:27:32.334459+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "oracle", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.0011445s", "timestamp": "2025-06-19T17:27:32.43608+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "sa", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "132.042µs", "timestamp": "2025-06-19T17:27:32.436233+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "mysql", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "82.958µs", "timestamp": "2025-06-19T17:27:32.436325+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "postgres", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "64.25µs", "timestamp": "2025-06-19T17:27:32.436395+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "postgres", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "64.041µs", "timestamp": "2025-06-19T17:27:32.436466+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "trustno1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00108775s", "timestamp": "2025-06-19T17:27:32.833126+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "oracle", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001132209s", "timestamp": "2025-06-19T17:27:33.035945+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "postgres", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000368625s", "timestamp": "2025-06-19T17:27:33.436811+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "postgres", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001123667s", "timestamp": "2025-06-19T17:27:33.834239+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "operator", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "159.75µs", "timestamp": "2025-06-19T17:27:33.834432+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "sa", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "78.083µs", "timestamp": "2025-06-19T17:27:33.834518+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "mysql", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "65.541µs", "timestamp": "2025-06-19T17:27:33.83459+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "mysql", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "87.917µs", "timestamp": "2025-06-19T17:27:33.834688+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "oracle", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001125292s", "timestamp": "2025-06-19T17:27:33.935543+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "postgres", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001093584s", "timestamp": "2025-06-19T17:27:34.335518+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "mysql", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001047834s", "timestamp": "2025-06-19T17:27:34.835717+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "mysql", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001203584s", "timestamp": "2025-06-19T17:27:34.936752+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "manager", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "171.458µs", "timestamp": "2025-06-19T17:27:34.936946+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "operator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "106.75µs", "timestamp": "2025-06-19T17:27:34.937063+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "sa", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "80.958µs", "timestamp": "2025-06-19T17:27:34.937152+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "sa", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "66.417µs", "timestamp": "2025-06-19T17:27:34.937225+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "postgres", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001067833s", "timestamp": "2025-06-19T17:27:35.036989+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "mysql", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116084s", "timestamp": "2025-06-19T17:27:35.437893+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "sa", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001116125s", "timestamp": "2025-06-19T17:27:35.938319+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "sa", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000978541s", "timestamp": "2025-06-19T17:27:36.037954+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "service", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "175.417µs", "timestamp": "2025-06-19T17:27:36.038177+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "manager", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "97.917µs", "timestamp": "2025-06-19T17:27:36.038282+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "operator", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "90.667µs", "timestamp": "2025-06-19T17:27:36.038387+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "operator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "72.667µs", "timestamp": "2025-06-19T17:27:36.038469+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "mysql", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001112375s", "timestamp": "2025-06-19T17:27:36.336592+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "sa", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116541s", "timestamp": "2025-06-19T17:27:36.836797+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "operator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001167208s", "timestamp": "2025-06-19T17:27:37.039614+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "operator", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000301167s", "timestamp": "2025-06-19T17:27:37.336884+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "support", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "207.625µs", "timestamp": "2025-06-19T17:27:37.337117+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "service", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "112µs", "timestamp": "2025-06-19T17:27:37.337237+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "manager", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "137.292µs", "timestamp": "2025-06-19T17:27:37.337387+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "manager", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "96.25µs", "timestamp": "2025-06-19T17:27:37.337491+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "sa", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001108542s", "timestamp": "2025-06-19T17:27:37.438964+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "operator", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00112775s", "timestamp": "2025-06-19T17:27:37.939413+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "manager", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001188459s", "timestamp": "2025-06-19T17:27:38.33866+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "manager", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001161584s", "timestamp": "2025-06-19T17:27:38.440116+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "user", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "215.166µs", "timestamp": "2025-06-19T17:27:38.440366+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "support", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "119.458µs", "timestamp": "2025-06-19T17:27:38.440497+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "service", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "120.167µs", "timestamp": "2025-06-19T17:27:38.440627+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "service", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "108.625µs", "timestamp": "2025-06-19T17:27:38.440746+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "operator", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001113375s", "timestamp": "2025-06-19T17:27:38.837908+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "manager", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001101125s", "timestamp": "2025-06-19T17:27:39.040681+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "service", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001150542s", "timestamp": "2025-06-19T17:27:39.441876+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "service", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001182875s", "timestamp": "2025-06-19T17:27:39.839089+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "demo", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "192.5µs", "timestamp": "2025-06-19T17:27:39.839318+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "user", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "126.875µs", "timestamp": "2025-06-19T17:27:39.839455+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "support", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "118.208µs", "timestamp": "2025-06-19T17:27:39.839596+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "support", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "99.458µs", "timestamp": "2025-06-19T17:27:39.839705+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "manager", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001095083s", "timestamp": "2025-06-19T17:27:39.940499+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "service", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00111525s", "timestamp": "2025-06-19T17:27:40.339748+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "support", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001028458s", "timestamp": "2025-06-19T17:27:40.840728+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "support", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001140417s", "timestamp": "2025-06-19T17:27:40.941628+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "public", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "224.041µs", "timestamp": "2025-06-19T17:27:40.941875+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "demo", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "156.083µs", "timestamp": "2025-06-19T17:27:40.942051+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "user", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "107.083µs", "timestamp": "2025-06-19T17:27:40.942169+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "user", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "105.75µs", "timestamp": "2025-06-19T17:27:40.942282+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "service", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116875s", "timestamp": "2025-06-19T17:27:41.041763+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "support", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116125s", "timestamp": "2025-06-19T17:27:41.442955+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "user", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000304958s", "timestamp": "2025-06-19T17:27:41.942566+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "user", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001142708s", "timestamp": "2025-06-19T17:27:42.042892+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "default", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "215.791µs", "timestamp": "2025-06-19T17:27:42.043126+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "public", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.875µs", "timestamp": "2025-06-19T17:27:42.043259+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "demo", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "133.5µs", "timestamp": "2025-06-19T17:27:42.043403+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "demo", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "101.5µs", "timestamp": "2025-06-19T17:27:42.043513+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "support", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001111958s", "timestamp": "2025-06-19T17:27:42.340825+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "user", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0010765s", "timestamp": "2025-06-19T17:27:42.841764+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "demo", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001089s", "timestamp": "2025-06-19T17:27:43.04458+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "demo", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001145417s", "timestamp": "2025-06-19T17:27:43.341972+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "anonymous", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "210.083µs", "timestamp": "2025-06-19T17:27:43.342202+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "default", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.833µs", "timestamp": "2025-06-19T17:27:43.342335+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "public", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "90.708µs", "timestamp": "2025-06-19T17:27:43.342432+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "public", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "93.042µs", "timestamp": "2025-06-19T17:27:43.342533+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "user", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001100875s", "timestamp": "2025-06-19T17:27:43.444017+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "demo", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001093166s", "timestamp": "2025-06-19T17:27:43.943627+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "public", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001148042s", "timestamp": "2025-06-19T17:27:44.343658+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "public", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001173959s", "timestamp": "2025-06-19T17:27:44.44518+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "passw0rd", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "183.834µs", "timestamp": "2025-06-19T17:27:44.445386+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "anonymous", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "145.917µs", "timestamp": "2025-06-19T17:27:44.44554+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "default", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "100.834µs", "timestamp": "2025-06-19T17:27:44.44565+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "default", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.417µs", "timestamp": "2025-06-19T17:27:44.445736+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "demo", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001066625s", "timestamp": "2025-06-19T17:27:44.842792+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "public", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099292s", "timestamp": "2025-06-19T17:27:45.045661+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "default", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001159958s", "timestamp": "2025-06-19T17:27:45.446882+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "default", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001136542s", "timestamp": "2025-06-19T17:27:45.843919+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "179.167µs", "timestamp": "2025-06-19T17:27:45.844131+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "passw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "120.125µs", "timestamp": "2025-06-19T17:27:45.844261+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "anonymous", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "87.959µs", "timestamp": "2025-06-19T17:27:45.844356+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "anonymous", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "117.875µs", "timestamp": "2025-06-19T17:27:45.844481+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "public", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001073083s", "timestamp": "2025-06-19T17:27:45.944661+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "default", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001085125s", "timestamp": "2025-06-19T17:27:46.344713+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "anonymous", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001124166s", "timestamp": "2025-06-19T17:27:46.845586+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "anonymous", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001152083s", "timestamp": "2025-06-19T17:27:46.945805+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "p@ssword", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "230.417µs", "timestamp": "2025-06-19T17:27:46.946064+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "125.792µs", "timestamp": "2025-06-19T17:27:46.946204+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "passw0rd", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "114.833µs", "timestamp": "2025-06-19T17:27:46.946331+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "passw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "119.833µs", "timestamp": "2025-06-19T17:27:46.946459+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "default", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001111458s", "timestamp": "2025-06-19T17:27:47.046736+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "anonymous", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001120709s", "timestamp": "2025-06-19T17:27:47.447967+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "passw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001275208s", "timestamp": "2025-06-19T17:27:47.947717+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "passw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001208291s", "timestamp": "2025-06-19T17:27:48.047935+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "253.042µs", "timestamp": "2025-06-19T17:27:48.048231+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "p@ssword", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "139.417µs", "timestamp": "2025-06-19T17:27:48.048394+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.958µs", "timestamp": "2025-06-19T17:27:48.048483+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "70.083µs", "timestamp": "2025-06-19T17:27:48.048605+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "anonymous", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00111475s", "timestamp": "2025-06-19T17:27:48.345788+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "passw0rd", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001149208s", "timestamp": "2025-06-19T17:27:48.846701+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001182209s", "timestamp": "2025-06-19T17:27:49.049764+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001202709s", "timestamp": "2025-06-19T17:27:49.346991+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "255.25µs", "timestamp": "2025-06-19T17:27:49.34729+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "105.25µs", "timestamp": "2025-06-19T17:27:49.347405+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "p@ssword", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "77.292µs", "timestamp": "2025-06-19T17:27:49.347489+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "p@ssword", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "84.125µs", "timestamp": "2025-06-19T17:27:49.347595+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "passw0rd", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001095291s", "timestamp": "2025-06-19T17:27:49.449031+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001049791s", "timestamp": "2025-06-19T17:27:49.948753+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "p@ssword", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001125125s", "timestamp": "2025-06-19T17:27:50.348697+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "p@ssword", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001174667s", "timestamp": "2025-06-19T17:27:50.450196+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "186.958µs", "timestamp": "2025-06-19T17:27:50.450401+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "97.875µs", "timestamp": "2025-06-19T17:27:50.450506+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "63.666µs", "timestamp": "2025-06-19T17:27:50.450576+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "75.375µs", "timestamp": "2025-06-19T17:27:50.450657+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "p@ssw0rd", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001066417s", "timestamp": "2025-06-19T17:27:50.847742+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "p@ssword", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099125s", "timestamp": "2025-06-19T17:27:51.050838+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00115425s", "timestamp": "2025-06-19T17:27:51.451793+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00111225s", "timestamp": "2025-06-19T17:27:51.848849+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "205.125µs", "timestamp": "2025-06-19T17:27:51.849077+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "154.083µs", "timestamp": "2025-06-19T17:27:51.849247+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "99.542µs", "timestamp": "2025-06-19T17:27:51.849354+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "116.084µs", "timestamp": "2025-06-19T17:27:51.849479+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "p@ssword", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001103625s", "timestamp": "2025-06-19T17:27:51.949816+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00111425s", "timestamp": "2025-06-19T17:27:52.349776+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.0011115s", "timestamp": "2025-06-19T17:27:52.850575+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001142583s", "timestamp": "2025-06-19T17:27:52.950949+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "guest123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "195.916µs", "timestamp": "2025-06-19T17:27:52.951168+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "114µs", "timestamp": "2025-06-19T17:27:52.951291+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root!", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "130.875µs", "timestamp": "2025-06-19T17:27:52.951432+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "109.667µs", "timestamp": "2025-06-19T17:27:52.951552+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001094125s", "timestamp": "2025-06-19T17:27:53.051891+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001135125s", "timestamp": "2025-06-19T17:27:53.452893+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001143958s", "timestamp": "2025-06-19T17:27:53.952675+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root!", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001143917s", "timestamp": "2025-06-19T17:27:54.053022+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "user123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "185.791µs", "timestamp": "2025-06-19T17:27:54.053229+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "guest123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "109µs", "timestamp": "2025-06-19T17:27:54.053351+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "77.291µs", "timestamp": "2025-06-19T17:27:54.053436+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "83.667µs", "timestamp": "2025-06-19T17:27:54.053526+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001126583s", "timestamp": "2025-06-19T17:27:54.350865+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001096625s", "timestamp": "2025-06-19T17:27:54.851627+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001143875s", "timestamp": "2025-06-19T17:27:55.054649+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00114s", "timestamp": "2025-06-19T17:27:55.351995+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "demo123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "195.167µs", "timestamp": "2025-06-19T17:27:55.352211+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "user123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "109.459µs", "timestamp": "2025-06-19T17:27:55.35233+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "guest123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "105.791µs", "timestamp": "2025-06-19T17:27:55.352444+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "guest123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "100.834µs", "timestamp": "2025-06-19T17:27:55.352556+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root!", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000137458s", "timestamp": "2025-06-19T17:27:55.452993+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001100834s", "timestamp": "2025-06-19T17:27:55.953744+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "guest123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000468584s", "timestamp": "2025-06-19T17:27:56.353004+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "guest123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001153833s", "timestamp": "2025-06-19T17:27:56.454135+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "default123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "193.625µs", "timestamp": "2025-06-19T17:27:56.454345+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "demo123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "114µs", "timestamp": "2025-06-19T17:27:56.454469+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "user123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "88.5µs", "timestamp": "2025-06-19T17:27:56.454566+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "user123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "84.125µs", "timestamp": "2025-06-19T17:27:56.454658+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001084666s", "timestamp": "2025-06-19T17:27:56.852674+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "guest123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001104708s", "timestamp": "2025-06-19T17:27:57.055723+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "user123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001148417s", "timestamp": "2025-06-19T17:27:57.455788+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "user123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00117375s", "timestamp": "2025-06-19T17:27:57.853844+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "218.292µs", "timestamp": "2025-06-19T17:27:57.854097+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "default123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "145.167µs", "timestamp": "2025-06-19T17:27:57.854262+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "demo123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "113.834µs", "timestamp": "2025-06-19T17:27:57.854387+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "demo123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "89µs", "timestamp": "2025-06-19T17:27:57.854486+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "guest123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001099291s", "timestamp": "2025-06-19T17:27:57.954818+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "user123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001134875s", "timestamp": "2025-06-19T17:27:58.354105+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "demo123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.0011545s", "timestamp": "2025-06-19T17:27:58.855619+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "demo123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001128125s", "timestamp": "2025-06-19T17:27:58.955942+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "195.958µs", "timestamp": "2025-06-19T17:27:58.956156+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "119.125µs", "timestamp": "2025-06-19T17:27:58.956286+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "default123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "90.709µs", "timestamp": "2025-06-19T17:27:58.956383+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "default123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "97.125µs", "timestamp": "2025-06-19T17:27:58.956489+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "user123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001084417s", "timestamp": "2025-06-19T17:27:59.056767+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "demo123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001116625s", "timestamp": "2025-06-19T17:27:59.456871+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "default123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000246209s", "timestamp": "2025-06-19T17:27:59.956713+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "default123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001168166s", "timestamp": "2025-06-19T17:28:00.057921+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "175.917µs", "timestamp": "2025-06-19T17:28:00.058128+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "101.208µs", "timestamp": "2025-06-19T17:28:00.058245+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "87.125µs", "timestamp": "2025-06-19T17:28:00.058339+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "87.542µs", "timestamp": "2025-06-19T17:28:00.058432+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "demo123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001145167s", "timestamp": "2025-06-19T17:28:00.355216+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "default123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001112916s", "timestamp": "2025-06-19T17:28:00.856701+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000223666s", "timestamp": "2025-06-19T17:28:01.058636+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001118917s", "timestamp": "2025-06-19T17:28:01.356328+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "124.958µs", "timestamp": "2025-06-19T17:28:01.356476+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "test@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "94.75µs", "timestamp": "2025-06-19T17:28:01.356579+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "91.458µs", "timestamp": "2025-06-19T17:28:01.356678+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "74.792µs", "timestamp": "2025-06-19T17:28:01.356758+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "default123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001086458s", "timestamp": "2025-06-19T17:28:01.457921+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00110025s", "timestamp": "2025-06-19T17:28:01.957781+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001186208s", "timestamp": "2025-06-19T17:28:02.35792+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001158125s", "timestamp": "2025-06-19T17:28:02.459079+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "134.375µs", "timestamp": "2025-06-19T17:28:02.459233+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "123@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "82.792µs", "timestamp": "2025-06-19T17:28:02.459323+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "71.333µs", "timestamp": "2025-06-19T17:28:02.459401+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "test@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "55.042µs", "timestamp": "2025-06-19T17:28:02.459461+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001135542s", "timestamp": "2025-06-19T17:28:02.857803+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001112959s", "timestamp": "2025-06-19T17:28:03.059711+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000252583s", "timestamp": "2025-06-19T17:28:03.459689+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001126292s", "timestamp": "2025-06-19T17:28:03.85892+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin2023", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "175.084µs", "timestamp": "2025-06-19T17:28:03.859117+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "password@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "112.25µs", "timestamp": "2025-06-19T17:28:03.859238+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "98.75µs", "timestamp": "2025-06-19T17:28:03.859346+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "123@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "126.625µs", "timestamp": "2025-06-19T17:28:03.859527+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000951125s", "timestamp": "2025-06-19T17:28:03.958696+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "test@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001034083s", "timestamp": "2025-06-19T17:28:04.35892+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001186708s", "timestamp": "2025-06-19T17:28:04.860692+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001185042s", "timestamp": "2025-06-19T17:28:04.960152+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin2024", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "231.875µs", "timestamp": "2025-06-19T17:28:04.960423+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "120.25µs", "timestamp": "2025-06-19T17:28:04.960555+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password@123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "85.417µs", "timestamp": "2025-06-19T17:28:04.960649+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "password@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "75.75µs", "timestamp": "2025-06-19T17:28:04.96073+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "test@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001107542s", "timestamp": "2025-06-19T17:28:05.06079+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "123@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00113625s", "timestamp": "2025-06-19T17:28:05.460795+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000237875s", "timestamp": "2025-06-19T17:28:05.960947+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password@123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001192167s", "timestamp": "2025-06-19T17:28:06.061978+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root2023", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "205.917µs", "timestamp": "2025-06-19T17:28:06.062208+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "admin2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "119.417µs", "timestamp": "2025-06-19T17:28:06.062342+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin2023", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "80.875µs", "timestamp": "2025-06-19T17:28:06.062433+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "73µs", "timestamp": "2025-06-19T17:28:06.062512+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "123@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001104s", "timestamp": "2025-06-19T17:28:06.359988+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "password@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00107325s", "timestamp": "2025-06-19T17:28:06.861733+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001153542s", "timestamp": "2025-06-19T17:28:07.063647+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001176958s", "timestamp": "2025-06-19T17:28:07.361155+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root2024", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "213.209µs", "timestamp": "2025-06-19T17:28:07.361394+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "147.833µs", "timestamp": "2025-06-19T17:28:07.361565+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin2024", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "108.083µs", "timestamp": "2025-06-19T17:28:07.361685+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "admin2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "88.208µs", "timestamp": "2025-06-19T17:28:07.361784+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "password@123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001074916s", "timestamp": "2025-06-19T17:28:07.461849+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin2023", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001148666s", "timestamp": "2025-06-19T17:28:07.962067+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001141875s", "timestamp": "2025-06-19T17:28:08.362905+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001169s", "timestamp": "2025-06-19T17:28:08.463007+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123456", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "208.042µs", "timestamp": "2025-06-19T17:28:08.463239+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "admin", "password": "root2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "132.208µs", "timestamp": "2025-06-19T17:28:08.463379+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root2023", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "93.167µs", "timestamp": "2025-06-19T17:28:08.463481+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "85.792µs", "timestamp": "2025-06-19T17:28:08.463576+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin2023", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001138084s", "timestamp": "2025-06-19T17:28:08.862839+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "admin2024", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001127083s", "timestamp": "2025-06-19T17:28:09.064739+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.00115075s", "timestamp": "2025-06-19T17:28:09.464705+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root2023", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00114725s", "timestamp": "2025-06-19T17:28:09.863991+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "182.792µs", "timestamp": "2025-06-19T17:28:09.864197+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "109.375µs", "timestamp": "2025-06-19T17:28:09.864328+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root2024", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "84.542µs", "timestamp": "2025-06-19T17:28:09.864421+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "admin", "password": "root2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "73.166µs", "timestamp": "2025-06-19T17:28:09.864501+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "admin2024", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001091291s", "timestamp": "2025-06-19T17:28:09.963148+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root2023", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00108825s", "timestamp": "2025-06-19T17:28:10.363959+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.0011465s", "timestamp": "2025-06-19T17:28:10.865628+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root2024", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000652291s", "timestamp": "2025-06-19T17:28:10.964764+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123456789", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "180.334µs", "timestamp": "2025-06-19T17:28:10.964961+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "102.334µs", "timestamp": "2025-06-19T17:28:10.965072+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123456", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "79.875µs", "timestamp": "2025-06-19T17:28:10.965159+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "77.458µs", "timestamp": "2025-06-19T17:28:10.965243+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root2023", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001103333s", "timestamp": "2025-06-19T17:28:11.065803+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "admin", "password": "root2024", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001101042s", "timestamp": "2025-06-19T17:28:11.465775+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000209416s", "timestamp": "2025-06-19T17:28:11.965906+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123456", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001139334s", "timestamp": "2025-06-19T17:28:12.066935+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "12345678", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "227.042µs", "timestamp": "2025-06-19T17:28:12.067185+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "145.625µs", "timestamp": "2025-06-19T17:28:12.067338+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "840.167µs", "timestamp": "2025-06-19T17:28:12.06819+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "202.542µs", "timestamp": "2025-06-19T17:28:12.068434+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "admin", "password": "root2024", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001178625s", "timestamp": "2025-06-19T17:28:12.365098+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123456", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001114416s", "timestamp": "2025-06-19T17:28:12.866704+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000811167s", "timestamp": "2025-06-19T17:28:13.069234+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001182875s", "timestamp": "2025-06-19T17:28:13.366284+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "12345", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "198.5µs", "timestamp": "2025-06-19T17:28:13.36651+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "129.667µs", "timestamp": "2025-06-19T17:28:13.366647+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123456789", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "114.292µs", "timestamp": "2025-06-19T17:28:13.366771+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "101.875µs", "timestamp": "2025-06-19T17:28:13.36688+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123456", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001077917s", "timestamp": "2025-06-19T17:28:13.466814+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001182791s", "timestamp": "2025-06-19T17:28:13.96705+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001250042s", "timestamp": "2025-06-19T17:28:14.368108+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123456789", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001258042s", "timestamp": "2025-06-19T17:28:14.468062+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234567890", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "282.5µs", "timestamp": "2025-06-19T17:28:14.468391+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "183.917µs", "timestamp": "2025-06-19T17:28:14.468597+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "12345678", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "223.542µs", "timestamp": "2025-06-19T17:28:14.468846+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "165.792µs", "timestamp": "2025-06-19T17:28:14.46904+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000892458s", "timestamp": "2025-06-19T17:28:14.867556+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123456789", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000344708s", "timestamp": "2025-06-19T17:28:15.069554+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001043083s", "timestamp": "2025-06-19T17:28:15.470073+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "12345678", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00119825s", "timestamp": "2025-06-19T17:28:15.868772+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234567", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "202.292µs", "timestamp": "2025-06-19T17:28:15.868998+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "148.708µs", "timestamp": "2025-06-19T17:28:15.869159+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "12345", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "112.875µs", "timestamp": "2025-06-19T17:28:15.869284+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "97µs", "timestamp": "2025-06-19T17:28:15.869389+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123456789", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000118041s", "timestamp": "2025-06-19T17:28:15.967148+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "12345678", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001132542s", "timestamp": "2025-06-19T17:28:16.369263+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001126708s", "timestamp": "2025-06-19T17:28:16.870497+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "12345", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001226792s", "timestamp": "2025-06-19T17:28:16.970241+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "291.166µs", "timestamp": "2025-06-19T17:28:16.97057+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "166.333µs", "timestamp": "2025-06-19T17:28:16.970759+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234567890", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "158µs", "timestamp": "2025-06-19T17:28:16.970942+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "113µs", "timestamp": "2025-06-19T17:28:16.971069+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "12345678", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001135375s", "timestamp": "2025-06-19T17:28:17.070679+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "12345", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001170416s", "timestamp": "2025-06-19T17:28:17.471222+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000774791s", "timestamp": "2025-06-19T17:28:17.971829+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234567890", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001169s", "timestamp": "2025-06-19T17:28:18.071846+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "000000", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "201.584µs", "timestamp": "2025-06-19T17:28:18.072087+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "151.625µs", "timestamp": "2025-06-19T17:28:18.072255+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234567", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "97.333µs", "timestamp": "2025-06-19T17:28:18.072365+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "65.708µs", "timestamp": "2025-06-19T17:28:18.072441+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "12345", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001193041s", "timestamp": "2025-06-19T17:28:18.370432+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234567890", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001156375s", "timestamp": "2025-06-19T17:28:18.871636+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001182791s", "timestamp": "2025-06-19T17:28:19.073603+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234567", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001159834s", "timestamp": "2025-06-19T17:28:19.371604+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "187.917µs", "timestamp": "2025-06-19T17:28:19.371817+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "132.083µs", "timestamp": "2025-06-19T17:28:19.371961+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "106.083µs", "timestamp": "2025-06-19T17:28:19.37208+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "80.209µs", "timestamp": "2025-06-19T17:28:19.372168+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234567890", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001092375s", "timestamp": "2025-06-19T17:28:19.472283+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234567", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001127125s", "timestamp": "2025-06-19T17:28:19.972959+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001209s", "timestamp": "2025-06-19T17:28:20.373357+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001209666s", "timestamp": "2025-06-19T17:28:20.473478+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "admin", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "205.709µs", "timestamp": "2025-06-19T17:28:20.473738+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "132.25µs", "timestamp": "2025-06-19T17:28:20.473891+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "000000", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "122.75µs", "timestamp": "2025-06-19T17:28:20.474029+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "120.041µs", "timestamp": "2025-06-19T17:28:20.474183+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234567", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001136208s", "timestamp": "2025-06-19T17:28:20.872742+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001141666s", "timestamp": "2025-06-19T17:28:21.074735+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001205375s", "timestamp": "2025-06-19T17:28:21.475373+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "000000", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.000705833s", "timestamp": "2025-06-19T17:28:21.87347+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "admin123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "284.25µs", "timestamp": "2025-06-19T17:28:21.873794+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "201.459µs", "timestamp": "2025-06-19T17:28:21.874049+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "161µs", "timestamp": "2025-06-19T17:28:21.874236+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "141.542µs", "timestamp": "2025-06-19T17:28:21.874393+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000224708s", "timestamp": "2025-06-19T17:28:21.973172+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "000000", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001176208s", "timestamp": "2025-06-19T17:28:22.374512+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.002502084s", "timestamp": "2025-06-19T17:28:22.876894+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001212958s", "timestamp": "2025-06-19T17:28:22.974765+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "root", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "173.459µs", "timestamp": "2025-06-19T17:28:22.974959+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "108.542µs", "timestamp": "2025-06-19T17:28:22.975077+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "admin", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "105.292µs", "timestamp": "2025-06-19T17:28:22.975189+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "95.5µs", "timestamp": "2025-06-19T17:28:22.975294+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "000000", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001179083s", "timestamp": "2025-06-19T17:28:23.075895+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000726834s", "timestamp": "2025-06-19T17:28:23.476093+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000464667s", "timestamp": "2025-06-19T17:28:23.977597+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "admin", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001174s", "timestamp": "2025-06-19T17:28:24.078256+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "pass", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "243.833µs", "timestamp": "2025-06-19T17:28:24.078535+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "116.666µs", "timestamp": "2025-06-19T17:28:24.078671+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "admin123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "92.75µs", "timestamp": "2025-06-19T17:28:24.078773+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "84.959µs", "timestamp": "2025-06-19T17:28:24.078867+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0008535s", "timestamp": "2025-06-19T17:28:24.375369+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "admin", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001144458s", "timestamp": "2025-06-19T17:28:24.878033+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001292583s", "timestamp": "2025-06-19T17:28:25.08014+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "admin123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001171416s", "timestamp": "2025-06-19T17:28:25.376623+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "test", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "252.166µs", "timestamp": "2025-06-19T17:28:25.37691+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "141.208µs", "timestamp": "2025-06-19T17:28:25.377133+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "root", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "153µs", "timestamp": "2025-06-19T17:28:25.377305+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "153.125µs", "timestamp": "2025-06-19T17:28:25.377484+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "admin", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001176666s", "timestamp": "2025-06-19T17:28:25.477248+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "admin123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001164875s", "timestamp": "2025-06-19T17:28:25.978743+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000335042s", "timestamp": "2025-06-19T17:28:26.377805+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "root", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001291709s", "timestamp": "2025-06-19T17:28:26.478559+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "guest", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "321.792µs", "timestamp": "2025-06-19T17:28:26.478929+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "190.041µs", "timestamp": "2025-06-19T17:28:26.479142+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "pass", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "176.75µs", "timestamp": "2025-06-19T17:28:26.479345+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "148.041µs", "timestamp": "2025-06-19T17:28:26.479508+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "admin123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000458792s", "timestamp": "2025-06-19T17:28:26.878473+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "root", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000874875s", "timestamp": "2025-06-19T17:28:27.081223+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001235541s", "timestamp": "2025-06-19T17:28:27.480741+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "pass", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00126s", "timestamp": "2025-06-19T17:28:27.879755+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "371.75µs", "timestamp": "2025-06-19T17:28:27.880217+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "217.375µs", "timestamp": "2025-06-19T17:28:27.880479+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "test", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "146.333µs", "timestamp": "2025-06-19T17:28:27.880656+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "158.542µs", "timestamp": "2025-06-19T17:28:27.880845+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "root", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001184333s", "timestamp": "2025-06-19T17:28:27.979912+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "pass", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001186667s", "timestamp": "2025-06-19T17:28:28.378978+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000313042s", "timestamp": "2025-06-19T17:28:28.881146+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "test", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001190542s", "timestamp": "2025-06-19T17:28:28.981116+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "244.875µs", "timestamp": "2025-06-19T17:28:28.981396+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "172.167µs", "timestamp": "2025-06-19T17:28:28.981605+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "guest", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "150.709µs", "timestamp": "2025-06-19T17:28:28.98177+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "166.916µs", "timestamp": "2025-06-19T17:28:28.981962+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "pass", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001126708s", "timestamp": "2025-06-19T17:28:29.08233+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "test", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001164125s", "timestamp": "2025-06-19T17:28:29.48192+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000942333s", "timestamp": "2025-06-19T17:28:29.982893+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "guest", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001229541s", "timestamp": "2025-06-19T17:28:30.083566+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "357.959µs", "timestamp": "2025-06-19T17:28:30.083968+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "206.625µs", "timestamp": "2025-06-19T17:28:30.084198+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "166.042µs", "timestamp": "2025-06-19T17:28:30.084385+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "193.375µs", "timestamp": "2025-06-19T17:28:30.084605+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "test", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001149958s", "timestamp": "2025-06-19T17:28:30.380112+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "guest", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001103084s", "timestamp": "2025-06-19T17:28:30.88224+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.000841708s", "timestamp": "2025-06-19T17:28:31.085433+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001172875s", "timestamp": "2025-06-19T17:28:31.381294+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123qwe", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "173.833µs", "timestamp": "2025-06-19T17:28:31.381519+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "106.5µs", "timestamp": "2025-06-19T17:28:31.381638+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "60.375µs", "timestamp": "2025-06-19T17:28:31.381707+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "69.958µs", "timestamp": "2025-06-19T17:28:31.381783+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "guest", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00035025s", "timestamp": "2025-06-19T17:28:31.48226+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000976875s", "timestamp": "2025-06-19T17:28:31.983851+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001101334s", "timestamp": "2025-06-19T17:28:32.382865+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.00121125s", "timestamp": "2025-06-19T17:28:32.483463+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "qwerty", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "170.916µs", "timestamp": "2025-06-19T17:28:32.483661+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "119.667µs", "timestamp": "2025-06-19T17:28:32.483794+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "68.334µs", "timestamp": "2025-06-19T17:28:32.48387+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "66.458µs", "timestamp": "2025-06-19T17:28:32.483944+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000811417s", "timestamp": "2025-06-19T17:28:32.883058+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "1234", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001176375s", "timestamp": "2025-06-19T17:28:33.086604+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001164791s", "timestamp": "2025-06-19T17:28:33.485098+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001248709s", "timestamp": "2025-06-19T17:28:33.884305+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "abc123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "178.25µs", "timestamp": "2025-06-19T17:28:33.8846+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "81.167µs", "timestamp": "2025-06-19T17:28:33.884695+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123qwe", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "59.125µs", "timestamp": "2025-06-19T17:28:33.884762+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "89.083µs", "timestamp": "2025-06-19T17:28:33.884861+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "1234", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.0010915s", "timestamp": "2025-06-19T17:28:33.984908+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000968333s", "timestamp": "2025-06-19T17:28:34.38382+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001176375s", "timestamp": "2025-06-19T17:28:34.886016+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123qwe", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001155416s", "timestamp": "2025-06-19T17:28:34.986071+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "Password1", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "221.5µs", "timestamp": "2025-06-19T17:28:34.986321+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "92.666µs", "timestamp": "2025-06-19T17:28:34.986425+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "qwerty", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "70.25µs", "timestamp": "2025-06-19T17:28:34.986504+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "68.583µs", "timestamp": "2025-06-19T17:28:34.986581+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "password1", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.000205166s", "timestamp": "2025-06-19T17:28:35.086796+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "123qwe", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.00110825s", "timestamp": "2025-06-19T17:28:35.48621+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:21: i/o timeout", "response_time": "1.001295208s", "timestamp": "2025-06-19T17:28:35.987855+08:00"}, {"protocol": "ssh", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "qwerty", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.2:22: i/o timeout", "response_time": "1.001140583s", "timestamp": "2025-06-19T17:28:36.087937+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "welcome", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "260.042µs", "timestamp": "2025-06-19T17:28:36.088235+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 21, "username": "administrator", "password": "Password1", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:21: connect: connection refused", "response_time": "150.667µs", "timestamp": "2025-06-19T17:28:36.088406+08:00"}, {"protocol": "ftp", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "abc123", "success": false, "error": "FTP dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "98.416µs", "timestamp": "2025-06-19T17:28:36.088517+08:00"}, {"protocol": "ssh", "target": "127.0.0.1", "port": 22, "username": "administrator", "password": "abc123", "success": false, "error": "SSH dial failed: dial tcp 127.0.0.1:22: connect: connection refused", "response_time": "90.875µs", "timestamp": "2025-06-19T17:28:36.088619+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 22, "username": "administrator", "password": "123qwe", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001184041s", "timestamp": "2025-06-19T17:28:36.384984+08:00"}, {"protocol": "ftp", "target": "127.0.0.2", "port": 21, "username": "administrator", "password": "qwerty", "success": false, "error": "FTP operation timeout after 2s: context deadline exceeded", "response_time": "2.001159208s", "timestamp": "2025-06-19T17:28:36.887148+08:00"}]