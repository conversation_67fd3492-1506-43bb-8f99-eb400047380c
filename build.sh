#!/bin/bash

# x-crack 跨平台构建脚本
# 支持构建所有目标平台的二进制文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="x-crack"
VERSION=${VERSION:-"2.0.0"}
GIT_COMMIT=${GIT_COMMIT:-$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")}
BUILD_TIME=${BUILD_TIME:-$(date -u '+%Y-%m-%d_%H:%M:%S')}

# 构建标志
LDFLAGS="-ldflags=-X=main.version=${VERSION} -X=main.gitCommit=${GIT_COMMIT} -X=main.buildTime=${BUILD_TIME}"

# 支持的平台列表
PLATFORMS="darwin/amd64 darwin/arm64 linux/amd64 linux/arm64 windows/amd64 windows/arm64"

# 函数：获取平台对应的二进制文件名
get_binary_name() {
    local platform=$1
    case $platform in
        "darwin/amd64")
            echo "${PROJECT_NAME}_darwin_amd64"
            ;;
        "darwin/arm64")
            echo "${PROJECT_NAME}_darwin_arm64"
            ;;
        "linux/amd64")
            echo "${PROJECT_NAME}_linux_amd64"
            ;;
        "linux/arm64")
            echo "${PROJECT_NAME}_linux_arm64"
            ;;
        "windows/amd64")
            echo "${PROJECT_NAME}_windows_amd64.exe"
            ;;
        "windows/arm64")
            echo "${PROJECT_NAME}_windows_arm64.exe"
            ;;
        *)
            echo ""
            ;;
    esac
}

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：显示帮助信息
show_help() {
    echo "x-crack 跨平台构建脚本"
    echo ""
    echo "用法: $0 [选项] [平台...]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -a, --all      构建所有平台"
    echo "  -c, --clean    构建前清理"
    echo "  -r, --release  创建发布包"
    echo "  -v, --verbose  详细输出"
    echo ""
    echo "支持的平台:"
    for platform in $PLATFORMS; do
        echo "  $platform"
    done
    echo ""
    echo "示例:"
    echo "  $0 --all                    # 构建所有平台"
    echo "  $0 darwin/amd64 linux/amd64 # 构建指定平台"
    echo "  $0 --clean --all            # 清理后构建所有平台"
    echo "  $0 --release                # 创建发布包"
}

# 函数：清理构建产物
clean_build() {
    print_info "清理构建产物..."
    rm -f ${PROJECT_NAME}
    for platform in $PLATFORMS; do
        local binary_name=$(get_binary_name "$platform")
        rm -f "$binary_name"
    done
    rm -rf release/
    rm -f release/*.tar.gz
    print_success "清理完成"
}

# 函数：构建指定平台
build_platform() {
    local platform=$1
    local binary_name=$(get_binary_name "$platform")

    if [[ -z "$binary_name" ]]; then
        print_error "不支持的平台: $platform"
        return 1
    fi
    
    local goos=$(echo $platform | cut -d'/' -f1)
    local goarch=$(echo $platform | cut -d'/' -f2)
    
    print_info "构建 $platform -> $binary_name"
    
    if [[ "$VERBOSE" == "true" ]]; then
        CGO_ENABLED=0 GOOS=$goos GOARCH=$goarch go build -tags "nocgo netgo" -ldflags "-w -s -extldflags '-static' -X main.version=${VERSION} -X main.gitCommit=${GIT_COMMIT} -X main.buildTime=${BUILD_TIME}" -o "$binary_name" -v
    else
        CGO_ENABLED=0 GOOS=$goos GOARCH=$goarch go build -tags "nocgo netgo" -ldflags "-w -s -extldflags '-static' -X main.version=${VERSION} -X main.gitCommit=${GIT_COMMIT} -X main.buildTime=${BUILD_TIME}" -o "$binary_name"
    fi
    
    if [[ $? -eq 0 ]]; then
        local size=$(ls -lh "$binary_name" | awk '{print $5}')
        print_success "构建成功: $binary_name ($size)"
    else
        print_error "构建失败: $platform"
        return 1
    fi
}

# 函数：构建所有平台
build_all() {
    print_info "开始构建所有平台..."
    local success_count=0
    local total_count=0

    # 计算总数
    for platform in $PLATFORMS; do
        ((total_count++))
    done

    for platform in $PLATFORMS; do
        if build_platform "$platform"; then
            ((success_count++))
        fi
    done
    
    echo ""
    print_info "构建完成: $success_count/$total_count 成功"
    
    if [[ $success_count -eq $total_count ]]; then
        print_success "所有平台构建成功！"
        return 0
    else
        print_warning "部分平台构建失败"
        return 1
    fi
}

# 函数：创建发布包
create_release() {
    print_info "创建发布包..."
    
    # 确保所有平台都已构建
    local missing_binaries=()
    for platform in $PLATFORMS; do
        local binary_name=$(get_binary_name "$platform")
        if [[ ! -f "$binary_name" ]]; then
            missing_binaries+=("$binary_name")
        fi
    done
    
    if [[ ${#missing_binaries[@]} -gt 0 ]]; then
        print_warning "以下二进制文件缺失，将重新构建:"
        for binary in "${missing_binaries[@]}"; do
            echo "  - $binary"
        done
        build_all
    fi
    
    # 创建发布目录
    mkdir -p release
    
    # 复制二进制文件
    for platform in $PLATFORMS; do
        local binary_name=$(get_binary_name "$platform")
        if [[ -f "$binary_name" ]]; then
            cp "$binary_name" release/
        fi
    done
    
    # 复制其他文件
    [[ -f "README.md" ]] && cp README.md release/
    [[ -f "config.yaml" ]] && cp config.yaml release/
    [[ -d "dict" ]] && cp -r dict release/
    
    # 创建压缩包
    local archive_name="x-crack-${VERSION}.tar.gz"
    tar -czf "release/$archive_name" -C release .
    
    print_success "发布包创建完成: release/$archive_name"
    
    # 显示文件列表
    print_info "发布包内容:"
    tar -tzf "release/$archive_name" | sed 's/^/  /'
}

# 主函数
main() {
    local platforms=()
    local build_all_flag=false
    local clean_flag=false
    local release_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                build_all_flag=true
                shift
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -r|--release)
                release_flag=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            *)
                # 检查是否是支持的平台
                local is_supported=false
                for supported_platform in $PLATFORMS; do
                    if [[ "$1" == "$supported_platform" ]]; then
                        is_supported=true
                        break
                    fi
                done

                if [[ "$is_supported" == "true" ]]; then
                    platforms+=("$1")
                else
                    print_error "未知选项或不支持的平台: $1"
                    echo "使用 --help 查看帮助信息"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 显示构建信息
    print_info "x-crack 跨平台构建脚本"
    print_info "版本: $VERSION"
    print_info "提交: $GIT_COMMIT"
    print_info "时间: $BUILD_TIME"
    echo ""
    
    # 执行清理
    if [[ "$clean_flag" == "true" ]]; then
        clean_build
        echo ""
    fi
    
    # 执行构建
    if [[ "$build_all_flag" == "true" ]]; then
        build_all
    elif [[ ${#platforms[@]} -gt 0 ]]; then
        for platform in "${platforms[@]}"; do
            build_platform "$platform"
        done
    elif [[ "$release_flag" == "true" ]]; then
        build_all
    else
        print_warning "未指定构建目标"
        echo "使用 --help 查看帮助信息"
        exit 1
    fi
    
    # 创建发布包
    if [[ "$release_flag" == "true" ]]; then
        echo ""
        create_release
    fi
    
    echo ""
    print_success "构建脚本执行完成！"
}

# 检查Go环境
if ! command -v go &> /dev/null; then
    print_error "Go 未安装或不在 PATH 中"
    exit 1
fi

# 执行主函数
main "$@"
