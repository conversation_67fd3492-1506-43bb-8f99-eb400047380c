# x-crack

一个功能强大且灵活的弱口令扫描工具，使用Go语言开发，支持多种协议的爆破攻击。

[![Go Report Card](https://goreportcard.com/badge/github.com/XTeam-Wing/x-crack)](https://goreportcard.com/report/github.com/XTeam-Wing/x-crack)
[![License](https://img.shields.io/badge/license-GPL%20v3-blue.svg)](LICENSE)
[![Release](https://img.shields.io/github/v/release/XTeam-Wing/x-crack.svg)](https://github.com/XTeam-Wing/x-crack/releases)

## 🌟 特性

- 🚀 **高性能**: 优化的限速器和双重并发控制机制，支持高效稳定的大规模扫描
- 🔧 **多协议支持**: SSH, FTP, Telnet, MySQL, PostgreSQL, Redis, MongoDB, HTTP/HTTPS, SMB, VNC, SNMP, IMAP, POP3, SMTP 等16种协议
- 📝 **灵活配置**: 支持YAML配置文件、命令行参数等多种配置方式
- 🎯 **精确控制**: 支持延迟控制、重试机制、动态限流调整等高级功能
- 📊 **多种输出**: 支持文本、JSON、CSV等输出格式
- 🔌 **可扩展**: 模块化设计，支持自定义协议处理器
- 📦 **SDK支持**: 可作为库导入到其他Go项目中，提供完整的API
- ⏱️ **超时控制**: 每个协议独立的连接超时设置
- 🔄 **容错机制**: 支持失败重试和断点续传
- 📈 **实时监控**: 支持实时并发状态监控和动态配置调整
- 🛡️ **安全防护**: 内置限流保护，避免对目标系统造成过大压力

## 📋 支持的协议

| 协议 | 默认端口 | 状态 | 描述 |
|------|----------|------|------|
| SSH | 22 | ✅ | Secure Shell 协议 |
| FTP | 21 | ✅ | File Transfer Protocol |
| Telnet | 23 | ✅ | 远程登录协议 |
| MySQL | 3306 | ✅ | MySQL 数据库 |
| PostgreSQL | 5432 | ✅ | PostgreSQL 数据库 |
| Redis | 6379 | ✅ | Redis 内存数据库 |
| MongoDB | 27017 | ✅ | MongoDB 文档数据库 |
| HTTP | 80, 8080, 8000, 8888 | ✅ | HTTP Basic Auth |
| HTTPS | 443, 8443 | ✅ | HTTPS Basic Auth |
| SMB | 445, 139 | ✅ | Server Message Block |
| VNC | 5900-5902 | ✅ | Virtual Network Computing |
| SNMP | 161 | ✅ | Simple Network Management Protocol |
| IMAP | 143, 993 | ✅ | Internet Message Access Protocol |
| POP3 | 110, 995 | ✅ | Post Office Protocol |
| SMTP | 25, 587, 465 | ✅ | Simple Mail Transfer Protocol |
| RDP | 3389 | ✅ | Remote Desktop Protocol |

## 🚀 快速开始

### 安装

```bash
# 从源码编译
git clone https://github.com/XTeam-Wing/x-crack.git
cd x-crack
go build -o x-crack ./cmd/x-crack

# 或直接下载发布版本
# 下载对应平台的二进制文件到 PATH 目录
```

### 基本使用

```bash
# 单目标单协议爆破
./x-crack -target ***********00 -protocol ssh -username root -password 123456

# 多用户名密码组合
./x-crack -target ***********00 -protocol ssh -usernames root,admin -passwords 123456,password

# 批量目标扫描
./x-crack -l ip.txt -protocols ssh

# 从文件读取目标和凭据
./x-crack -target-file targets.txt -user-file users.txt -pass-file passwords.txt -protocol ssh

# 高性能配置（推荐用于生产环境）
./x-crack -target ***********00 -protocol ssh \
  -target-concurrent 10 -task-concurrent 5 \
  -timeout 10s -retries 2 -delay 200ms

# 高速扫描配置（谨慎使用）
./x-crack -target ***********00 -protocol ssh \
  -target-concurrent 20 -task-concurrent 10 \
  -timeout 5s -delay 100ms

# 输出到文件并显示详细信息
./x-crack -target ***********00 -protocol ssh \
  -output results.json -format json -verbose -show-failed
```

### 配置文件

x-crack 支持YAML格式的配置文件，通过 `-config` 参数指定。配置文件支持完整的参数设置：

```yaml
# x-crack config file
# generated by https://github.com/projectdiscovery/goflags

# target host (e.g. ***********)
#target: 

# target hosts (comma separated)
#targets: []

# file containing target hosts
#target-file: 

# target port
#port: 0

# port range (e.g. 22,3389,1433-1434)
#ports: 

# file containing ports
#port-file: 

# protocol to use (ssh,mysql,ftp,etc.)
#protocol: 

# protocols to use (comma separated)
#protocols: []

# username for authentication
#username: 

# usernames (comma separated)
#usernames: []

# file containing usernames
#user-file: 

# password for authentication
#password: 

# passwords (comma separated)
#passwords: []

# file containing passwords
#pass-file: 

# file containing username:password combinations
#userpass-file: 

# number of concurrent targets
#target-concurrent: 50

# number of concurrent tasks per target
#task-concurrent: 1

# delay between requests (e.g. 100ms)
#delay: 

# timeout for each request
#timeout: 10s

# number of retries for failed requests
#retries: 3

# stop after first successful authentication
#ok-to-stop: false

# output file path
#output: 

# output format (text,json,csv)
#format: text

# verbose output
#verbose: false

# silent mode
#silent: false

# disable colored output
#no-color: false

# show failed authentication attempts
#show-failed: false

# configuration file path
#config: 

# show version information
#version: false

# show help message
```

使用配置文件：

```bash
# 使用默认配置文件
./x-crack -config config.yaml

# 配置文件 + 命令行参数 (命令行参数优先级更高)
./x-crack -config config.yaml -target ***********00 -protocol ssh
```

## 🔧 命令行参数

```bash
Usage:
  ./x-crack [flags]

目标设置:
   -target string       目标主机 (例如: ***********)
   -targets string[]    目标主机列表 (逗号分隔)
   -target-file string  包含目标主机的文件
   -port int            目标端口
   -ports string        端口范围 (例如: 22,3389,1433-1434)
   -port-file string    包含端口的文件
   -protocol string     使用的协议 (ssh,mysql,ftp等)
   -protocols string[]  协议列表 (逗号分隔)

认证设置:
   -u, -username string    认证用户名
   -usernames string[]     用户名列表 (逗号分隔)
   -uf, -user-file string  包含用户名的文件
   -p, -password string    认证密码
   -passwords string[]     密码列表 (逗号分隔)
   -pf, -pass-file string  包含密码的文件
   -userpass-file string   包含用户名:密码组合的文件

爆破设置 (v2.0优化):
   -target-concurrent int  全局最大并发数 (默认: 10, 推荐: 5-20)
   -task-concurrent int    单目标最大并发数 (默认: 5, 推荐: 3-10)
   -delay string           请求间最小延迟 (默认: 200ms, 推荐: 100ms-1s)
   -timeout string         每个请求的超时时间 (默认: 10s)
   -retries int            失败重试次数 (默认: 2, 推荐: 1-3)
   -ok-to-stop             首次成功认证后停止 (默认: false)

输出设置:
   -output string  输出文件路径
   -format string  输出格式 (text,json,csv) (默认: text)
   -v, -verbose    详细输出
   -d, -debug      调试模式
   -silent         静默模式
   -no-color       禁用彩色输出
   -show-failed    显示失败的认证尝试
   -show-progress  显示进度条 (默认: true)

其他设置:
   -config string  配置文件路径
   -version        显示版本信息
   -help           显示帮助信息
```

### 📋 性能调优指南

#### 🔥 高性能配置
适用于内部网络或测试环境：
```bash
./x-crack -target ***********00 -protocol ssh \
  -target-concurrent 20 -task-concurrent 10 \
  -delay 100ms -timeout 5s -retries 1
```

#### ⚖️ 平衡配置（推荐）
适用于生产环境，平衡速度和稳定性：
```bash
./x-crack -target ***********00 -protocol ssh \
  -target-concurrent 10 -task-concurrent 5 \
  -delay 200ms -timeout 10s -retries 2
```

#### 🛡️ 隐蔽配置
适用于需要低调扫描的场景：
```bash
./x-crack -target ***********00 -protocol ssh \
  -target-concurrent 3 -task-concurrent 2 \
  -delay 1s -timeout 15s -retries 1
```

## 📚 SDK 使用

x-crack 可以作为 Go 库导入到其他项目中，提供完整的编程接口：

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/XTeam-Wing/x-crack/pkg/brute"
)

func main() {
    // 创建优化后的配置
    config := &brute.Config{
        TargetConcurrent: 10,                     // 全局最大并发数
        TaskConcurrent:   5,                      // 单目标最大并发数  
        MinDelay:         time.Millisecond * 200, // 最小延迟
        Timeout:          time.Second * 10,       // 超时时间
        MaxRetries:       2,                      // 最大重试次数
        OkToStop:         false,                  // 成功后不停止
        ShowProgress:     true,                   // 显示进度
    }
    
    // 创建上下文
    ctx := context.Background()
    
    // 创建爆破引擎
    engine, err := brute.NewEngine(ctx, config)
    if err != nil {
        log.Fatalf("Failed to create engine: %v", err)
    }
    
    // 设置结果回调
    engine.SetResultCallback(func(result *brute.BruteResult) {
        if result.Success {
            fmt.Printf("✅ SUCCESS: %s %s:%s@%s:%d [%v]\n", 
                result.Item.Type, 
                result.Item.Username, 
                result.Item.Password, 
                result.Item.Target, 
                result.Item.Port,
                result.ResponseTime)
        } else if config.ShowProgress {
            fmt.Printf("❌ FAILED: %s %s:%s@%s:%d - %v [%v]\n", 
                result.Item.Type, 
                result.Item.Username, 
                result.Item.Password, 
                result.Item.Target, 
                result.Item.Port,
                result.Error,
                result.ResponseTime)
        }
    })
    
    // 创建爆破任务
    items := []*brute.BruteItem{
        {
            Type:     "ssh",
            Target:   "***********00",
            Port:     22,
            Username: "root",
            Password: "123456",
            Context:  ctx,
            Timeout:  time.Second * 10,
        },
        {
            Type:     "ssh", 
            Target:   "***********00",
            Port:     22,
            Username: "admin",
            Password: "password",
            Context:  ctx,
            Timeout:  time.Second * 10,
        },
    }
    
    // 执行爆破任务
    fmt.Println("🚀 Starting brute force attack...")
    
    // 监控并发状态（可选）
    go func() {
        ticker := time.NewTicker(5 * time.Second)
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                globalUsed, globalTotal, targetUsed, targetTotal := engine.GetConcurrencyStatus()
                fmt.Printf("📊 Concurrency: Global(%d/%d) Target(%d/%d)\n", 
                    globalUsed, globalTotal, targetUsed, targetTotal)
                    
                limit, burst := engine.GetRateLimitStatus()
                fmt.Printf("📈 Rate Limit: %v/burst, Burst: %d\n", limit, burst)
                
            case <-ctx.Done():
                return
            }
        }
    }()
    
    // 批量处理
    err = engine.BatchProcess(items)
    if err != nil {
        log.Fatalf("Batch process failed: %v", err)
    }
    
    // 动态调整限流器（可选）
    fmt.Println("🔧 Adjusting rate limiter...")
    err = engine.UpdateRateLimit(time.Millisecond*100, 20)
    if err != nil {
        log.Printf("Failed to update rate limit: %v", err)
    }
    
    fmt.Printf("✅ Completed! Processed %d items\n", engine.GetProcessedCount())
}
```

### 🎯 高级用法示例

#### 1. 自定义协议处理器
```go
// 设置自定义回调
config.CustomCallback = func(item *brute.BruteItem) *brute.BruteResult {
    start := time.Now()
    
    // 自定义处理逻辑
    success, err := customProtocolHandler(item)
    
    return &brute.BruteResult{
        Item:         item,
        Success:      success,
        Error:        err,
        ResponseTime: time.Since(start),
    }
}
```

#### 2. 实时监控和动态调整
```go
// 实时监控
go func() {
    for {
        time.Sleep(10 * time.Second)
        
        // 获取状态
        globalUsed, globalTotal, _, _ := engine.GetConcurrencyStatus()
        
        // 动态调整
        if globalUsed < globalTotal/2 {
            // 如果并发使用率低，可以适当提高速度
            engine.UpdateRateLimit(time.Millisecond*100, globalTotal*2)
        }
    }
}()
```

## 🏗️ 项目结构

```
x-crack/
├── cmd/
│   └── x-crack/            # CLI 应用入口
│       └── main.go
├── pkg/
│   ├── brute/              # 爆破引擎核心
│   │   ├── engine.go       # 爆破引擎 (v2.0优化)
│   │   ├── builder.go      # 构建器模式
│   │   ├── types.go        # 类型定义
│   │   └── engine_test.go  # 完整测试套件
│   ├── config/             # 配置管理
│   │   └── config.go       # 配置加载和保存
│   ├── protocols/          # 协议实现
│   │   ├── ssh.go          # SSH 协议
│   │   ├── mysql.go        # MySQL 协议
│   │   ├── ftp.go          # FTP 协议
│   │   ├── ...             # 其他协议
│   │   └── register.go     # 协议注册器
│   └── utils/              # 工具函数
│       └── utils.go
├── examples/               # 示例代码
│   └── brute_demo.go      # 完整的使用示例
├── dict/                   # 默认字典文件
│   ├── usernames.txt
│   ├── passwords.txt
│   └── combo.txt
├── test/                   # 测试文件
├── docs/                   # 文档目录
│   ├── RATE_LIMITER_FIX.md # 限速器修复说明
│   └── API.md              # API文档
├── config.yaml             # 默认配置文件
├── go.mod
├── go.sum
└── README.md
```

## ⚡ 性能特性与优化 (v2.0)

### 🚀 双重并发控制架构
```
全局并发控制 (globalSem)     # 控制所有目标的总并发数
    ↓
目标级并发控制 (targetSem)    # 控制单个目标的并发数
    ↓  
限流器控制 (rate.Limiter)     # 控制请求间隔
    ↓
实际执行任务
```

### 📊 核心性能特性
- **智能限流**: 基于 `golang.org/x/time/rate` 的高精度限流器
- **内存优化**: 流式处理，支持大规模扫描而不占用过多内存
- **连接复用**: 自动连接池和资源管理，减少连接开销
- **超时控制**: 每个协议独立的连接超时设置
- **重试机制**: 智能重试失败的连接，避免误报
- **实时监控**: 支持运行时状态监控和动态配置调整
- **信号量管理**: 防止goroutine泄漏的完善信号量管理

### 🔧 性能调优建议

#### 1. 基于目标数量的配置建议
```bash
# 单目标扫描
target_concurrent: 1
task_concurrent: 10
delay: 100ms

# 小规模扫描 (1-10个目标)
target_concurrent: 5
task_concurrent: 5
delay: 200ms

# 中等规模扫描 (10-100个目标)
target_concurrent: 10
task_concurrent: 3
delay: 300ms

# 大规模扫描 (100+个目标)
target_concurrent: 20
task_concurrent: 2
delay: 500ms
```

#### 2. 基于网络环境的配置建议
```bash
# 内网环境（低延迟）
delay: 50ms-100ms
timeout: 5s

# 公网环境（高延迟）
delay: 200ms-500ms
timeout: 15s

# 受限网络（需要隐蔽）
delay: 1s-3s
timeout: 30s
```

### 📈 监控和诊断

#### 实时状态监控
```go
// 获取并发状态
globalUsed, globalTotal, targetUsed, targetTotal := engine.GetConcurrencyStatus()
fmt.Printf("Global: %d/%d, Target: %d/%d\n", globalUsed, globalTotal, targetUsed, targetTotal)

// 获取限流器状态
limit, burst := engine.GetRateLimitStatus()
fmt.Printf("Rate: %v, Burst: %d\n", limit, burst)

// 获取处理计数
processed := engine.GetProcessedCount()
fmt.Printf("Processed: %d items\n", processed)
```

#### 性能问题诊断
1. **并发过高**: 如果出现连接拒绝，降低 `target_concurrent` 或 `task_concurrent`
2. **速度过慢**: 如果扫描速度慢，可以适当降低 `delay` 或增加并发数
3. **内存占用高**: 检查是否有goroutine泄漏，确保正确使用信号量
4. **网络超时**: 增加 `timeout` 值或降低并发数

## 🧪 测试和验证

### 运行测试套件
```bash
# 运行所有测试
go test ./...

# 运行核心引擎测试
go test ./pkg/brute -v

# 运行性能测试
go test ./pkg/brute -bench=.

# 测试覆盖率
go test ./pkg/brute -cover
```

### 测试验证功能
我们提供了完整的测试套件来验证核心功能：

1. **并发控制测试**: 验证并发数不超过配置限制
2. **限流器测试**: 验证请求间隔符合延迟设置  
3. **动态调整测试**: 验证运行时配置更新功能
4. **状态监控测试**: 验证并发状态获取功能

### 故障排除

#### 常见问题

**Q: 为什么扫描速度很慢？**
A: 检查以下配置：
- `min_delay` 是否设置过大（建议100ms-500ms）
- `target_concurrent` 和 `task_concurrent` 是否过小
- 网络延迟是否较高，考虑增加 `timeout` 值

**Q: 为什么会出现大量连接失败？**  
A: 可能原因：
- 并发数设置过高，目标系统无法处理
- 延迟设置过小，触发了目标系统的保护机制
- 建议降低并发数或增加延迟

**Q: 如何避免被目标系统检测？**
A: 隐蔽扫描建议：
- 使用较低的并发数（target_concurrent: 1-3）
- 增加请求间延迟（delay: 1s-5s）
- 随机化用户代理和请求头
- 分时段进行扫描

**Q: 内存占用过高怎么办？**
A: 优化建议：
- 检查是否有goroutine泄漏
- 降低并发数设置
- 确保正确处理结果回调
- 使用流式处理而不是全量加载

#### 调试模式
```bash
# 启用调试模式
./x-crack -debug -target ***********00 -protocol ssh

# 详细输出模式
./x-crack -verbose -show-failed -target ***********00 -protocol ssh
```

## 🔒 安全说明

**⚠️ 重要免责声明**: 本工具仅用于授权的安全测试和教育目的。请确保：

### 使用要求
1. **授权测试**: 仅在您拥有或已获得明确授权的系统上使用
2. **法律合规**: 遵守当地法律法规和公司政策  
3. **道德使用**: 不要用于恶意攻击或非法入侵
4. **测试环境**: 建议在测试环境中验证功能后再用于生产环境

### 安全最佳实践
1. **限制并发**: 避免设置过高的并发数，防止拒绝服务
2. **延迟控制**: 使用适当的延迟，避免对目标系统造成压力
3. **日志安全**: 确保日志文件的安全存储，避免凭据泄露
4. **网络隔离**: 在隔离的网络环境中进行测试
5. **及时清理**: 测试完成后及时清理相关日志和临时文件

### 责任声明
使用本工具进行任何形式的未授权访问都是违法行为。开发者不对工具的误用承担任何责任。用户需自行承担使用风险和法律责任。

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/new-protocol`)
3. 提交更改 (`git commit -am 'Add new protocol support'`)
4. 推送到分支 (`git push origin feature/new-protocol`)
5. 创建 Pull Request

### 添加新协议

要添加新协议支持，请：

1. 在 `pkg/protocols/` 目录下创建新的协议文件
2. 实现 `ProtocolHandler` 接口
3. 在 `register.go` 中注册新协议
4. 添加相应的测试

## 📄 许可证

本项目采用 GPL-3.0 许可证。详见 [LICENSE](LICENSE) 文件。

## 🔗 相关链接

- [Kscan](https://github.com/lcvvvv/kscan)
- [Yaklang](https://github.com/yaklang/yaklang)
- [GitHub 仓库](https://github.com/XTeam-Wing/x-crack)
- [问题反馈](https://github.com/XTeam-Wing/x-crack/issues)
- [Wiki 文档](https://github.com/XTeam-Wing/x-crack/wiki)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
