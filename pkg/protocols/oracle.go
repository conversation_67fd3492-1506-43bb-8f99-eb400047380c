package protocols

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/XTeam-Wing/x-crack/pkg/brute"
	_ "github.com/sijms/go-ora/v2"
)

// OracleBrute Oracle爆破
func OracleBrute(item *brute.BruteItem) *brute.BruteResult {
	result := &brute.BruteResult{
		Item:    item,
		Success: false,
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), item.Timeout)
	defer cancel()

	// 构建DSN连接字符串，添加超时参数
	timeoutSec := int(item.Timeout.Seconds())
	if timeoutSec <= 0 {
		timeoutSec = 30 // 默认30秒超时
	}

	// 构建带超时参数的DSN
	params := url.Values{}
	params.Set("TIMEOUT", strconv.Itoa(timeoutSec))            // 连接超时
	params.Set("CONNECTION TIMEOUT", strconv.Itoa(timeoutSec)) // 连接建立超时
	params.Set("TRACE FILE", "")                               // 禁用trace文件

	dsn := fmt.Sprintf("oracle://%s:%s@%s:%d/orcl?%s",
		url.QueryEscape(item.Username),
		url.QueryEscape(item.Password),
		item.Target,
		item.Port,
		params.Encode(),
	)

	db, err := sql.Open("oracle", dsn)
	if err != nil {
		result.Error = fmt.Errorf("failed to create Oracle connection: %w", err)
		return result
	}

	// 确保连接关闭
	defer func() {
		if db != nil {
			db.Close()
		}
	}()

	// 设置连接池参数和超时
	db.SetMaxOpenConns(1)
	db.SetMaxIdleConns(0)
	db.SetConnMaxLifetime(item.Timeout)
	db.SetConnMaxIdleTime(time.Second * 5) // 空闲连接5秒后关闭

	// 使用带上下文的Ping验证连接
	if err := db.PingContext(ctx); err != nil {
		result.Error = fmt.Errorf("failed to connect to Oracle: %w", err)
		return result
	}

	// 执行一个简单的查询来进一步验证连接
	var version string
	err = db.QueryRowContext(ctx, "SELECT banner FROM v$version WHERE ROWNUM = 1").Scan(&version)
	if err != nil {
		// 如果查询v$version失败，尝试更简单的查询
		var dummy string
		err = db.QueryRowContext(ctx, "SELECT 'Oracle Connection OK' FROM dual").Scan(&dummy)
		if err != nil {
			result.Error = fmt.Errorf("failed to verify Oracle connection: %w", err)
			return result
		}
		version = "Oracle Database (version query failed)"
	}

	result.Success = true
	result.Banner = fmt.Sprintf("Oracle connection successful - %s", version)
	return result
}
