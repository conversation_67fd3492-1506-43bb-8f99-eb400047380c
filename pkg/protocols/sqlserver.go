package protocols

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/XTeam-Wing/x-crack/pkg/brute"
	_ "github.com/denisenkom/go-mssqldb"
)

// MssqlBrute Mssql爆破
func MssqlBrute(item *brute.BruteItem) *brute.BruteResult {
	result := &brute.BruteResult{
		Item:    item,
		Success: false,
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), item.Timeout)
	defer cancel()

	// 构建DSN连接字符串，添加超时参数
	timeoutSec := int(item.Timeout.Seconds())
	if timeoutSec <= 0 {
		timeoutSec = 30 // 默认30秒超时
	}

	// 构建带超时参数的DSN
	params := url.Values{}
	params.Set("database", "master")
	params.Set("connection timeout", strconv.Itoa(timeoutSec)) // 连接超时
	params.Set("dial timeout", strconv.Itoa(timeoutSec))       // 拨号超时
	params.Set("encrypt", "disable")                           // 禁用加密以提高兼容性
	params.Set("trustservercertificate", "true")               // 信任服务器证书

	dsn := fmt.Sprintf("sqlserver://%s:%s@%s:%d?%s",
		url.QueryEscape(item.Username),
		url.QueryEscape(item.Password),
		item.Target,
		item.Port,
		params.Encode(),
	)

	db, err := sql.Open("sqlserver", dsn)
	if err != nil {
		result.Error = fmt.Errorf("failed to create Mssql connection: %w", err)
		return result
	}

	// 确保连接关闭
	defer func() {
		if db != nil {
			db.Close()
		}
	}()

	// 设置连接池参数和超时
	db.SetMaxOpenConns(1)
	db.SetMaxIdleConns(0)
	db.SetConnMaxLifetime(item.Timeout)
	db.SetConnMaxIdleTime(time.Second * 5) // 空闲连接5秒后关闭

	// 使用带上下文的Ping验证连接
	if err := db.PingContext(ctx); err != nil {
		result.Error = fmt.Errorf("failed to connect to Mssql: %w", err)
		return result
	}

	// 执行一个简单的查询来进一步验证连接
	var version string
	err = db.QueryRowContext(ctx, "SELECT @@VERSION").Scan(&version)
	if err != nil {
		// 如果查询@@VERSION失败，尝试更简单的查询
		var dummy int
		err = db.QueryRowContext(ctx, "SELECT 1").Scan(&dummy)
		if err != nil {
			result.Error = fmt.Errorf("failed to verify MSSQL connection: %w", err)
			return result
		}
		version = "Microsoft SQL Server (version query failed)"
	}

	result.Success = true
	result.Banner = fmt.Sprintf("MSSQL connection successful - %s", version)
	return result
}
