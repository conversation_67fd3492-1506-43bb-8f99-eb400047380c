package protocols

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/XTeam-Wing/x-crack/pkg/brute"
	"github.com/hirochachacha/go-smb2"
)

// SMBBrute SMB爆破
func SMBBrute(item *brute.BruteItem) *brute.BruteResult {
	result := &brute.BruteResult{
		Item:    item,
		Success: false,
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), item.Timeout)
	defer cancel()

	address := fmt.Sprintf("%s:%d", item.Target, item.Port)

	// 使用上下文控制的TCP连接
	dialer := &net.Dialer{
		Timeout: item.Timeout,
	}

	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		result.Error = fmt.Errorf("failed to connect to SMB server: %w", err)
		return result
	}
	defer func() {
		if conn != nil {
			conn.Close()
		}
	}()

	// 设置连接超时
	if err := conn.SetDeadline(time.Now().Add(item.Timeout)); err != nil {
		result.Error = fmt.Errorf("failed to set connection deadline: %w", err)
		return result
	}

	// 创建SMB2客户端
	d := &smb2.Dialer{
		Initiator: &smb2.NTLMInitiator{
			User:     item.Username,
			Password: item.Password,
			Domain:   "", // 可以为空，让服务器自动处理
		},
	}

	s, err := d.Dial(conn)
	if err != nil {
		// 区分不同类型的SMB错误
		if strings.Contains(err.Error(), "authentication") ||
			strings.Contains(err.Error(), "logon failure") ||
			strings.Contains(err.Error(), "invalid credentials") {
			result.Error = fmt.Errorf("SMB authentication failed: invalid credentials")
		} else if strings.Contains(err.Error(), "protocol") {
			result.Error = fmt.Errorf("SMB protocol error: %w", err)
		} else {
			result.Error = fmt.Errorf("failed to establish SMB session: %w", err)
		}
		return result
	}
	defer func() {
		if s != nil {
			s.Logoff()
		}
	}()

	// 尝试多个共享来验证认证
	shares := []string{"IPC$", "C$", "ADMIN$"}
	var lastErr error
	var connectedShare string

	for _, share := range shares {
		fs, err := s.Mount(share)
		if err != nil {
			lastErr = err
			continue
		}

		// 成功连接到共享
		fs.Umount()
		connectedShare = share
		break
	}

	// 如果所有共享都失败，检查是否是认证问题
	if connectedShare == "" {
		if lastErr != nil {
			if strings.Contains(lastErr.Error(), "access denied") ||
				strings.Contains(lastErr.Error(), "permission denied") {
				// 认证成功但没有访问权限，这仍然算认证成功
				result.Success = true
				result.Banner = "SMB authentication successful (limited access)"
				return result
			}
			result.Error = fmt.Errorf("failed to access SMB shares: %w", lastErr)
		} else {
			result.Error = fmt.Errorf("no accessible SMB shares found")
		}
		return result
	}

	// 尝试获取服务器信息
	serverInfo := "SMB authentication successful"
	if connectedShare != "" {
		serverInfo = fmt.Sprintf("SMB authentication successful - accessed %s share", connectedShare)
	}

	result.Success = true
	result.Banner = serverInfo
	return result
}
