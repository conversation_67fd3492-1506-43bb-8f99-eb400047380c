#!/bin/bash

# x-crack 跨平台编译演示脚本
# 展示如何使用Makefile进行跨平台编译

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "x-crack 跨平台编译演示"
echo "========================================"

print_info "项目已配置支持以下平台的跨平台编译："
echo ""
echo "支持的目标平台："
echo "  • darwin/amd64  (macOS Intel)"
echo "  • darwin/arm64  (macOS Apple Silicon)"
echo "  • linux/amd64   (Linux x86_64)"
echo "  • linux/arm64   (Linux ARM64)"
echo "  • windows/amd64 (Windows x86_64)"
echo "  • windows/arm64 (Windows ARM64)"
echo ""

print_info "可用的Makefile目标："
echo ""
echo "单平台构建："
echo "  make build-darwin-amd64   # 构建macOS Intel版本"
echo "  make build-darwin-arm64   # 构建macOS Apple Silicon版本"
echo "  make build-linux-amd64    # 构建Linux x86_64版本"
echo "  make build-linux-arm64    # 构建Linux ARM64版本"
echo "  make build-windows-amd64  # 构建Windows x86_64版本"
echo "  make build-windows-arm64  # 构建Windows ARM64版本"
echo ""
echo "批量构建："
echo "  make build-all            # 构建所有6个平台"
echo "  make build-all-legacy     # 构建传统3个平台"
echo ""
echo "发布管理："
echo "  make release              # 创建包含所有平台的发布包"
echo "  make clean                # 清理构建产物"
echo ""

print_info "构建脚本选项："
echo ""
echo "使用build.sh脚本："
echo "  ./build.sh --all                    # 构建所有平台"
echo "  ./build.sh darwin/amd64 linux/amd64 # 构建指定平台"
echo "  ./build.sh --clean --all            # 清理后构建所有平台"
echo "  ./build.sh --release                # 创建发布包"
echo ""

print_warning "注意事项："
echo ""
echo "1. 项目包含CGO依赖，可能在某些平台上需要特殊处理"
echo "2. 建议在目标平台上进行最终测试"
echo "3. Windows版本会自动添加.exe扩展名"
echo "4. 所有构建都使用CGO_ENABLED=0以确保静态链接"
echo ""

print_info "示例：构建当前平台版本"
echo "========================================"

if make build 2>/dev/null; then
    print_success "当前平台构建成功！"
    if [[ -f "x-crack" ]]; then
        size=$(ls -lh x-crack | awk '{print $5}')
        print_info "生成的二进制文件: x-crack ($size)"
    fi
else
    print_warning "当前平台构建遇到问题，这可能是由于CGO依赖导致的"
    print_info "您可以尝试以下解决方案："
    echo ""
    echo "1. 安装必要的C库依赖"
    echo "2. 使用Docker进行构建"
    echo "3. 在目标平台上直接构建"
    echo ""
fi

print_info "查看完整帮助信息："
echo "  make help                 # 查看所有可用目标"
echo "  ./build.sh --help         # 查看构建脚本帮助"
echo ""

print_success "跨平台编译配置完成！"
echo ""
echo "要开始构建，请运行："
echo "  make build-all            # 构建所有平台"
echo "  或"
echo "  ./build.sh --all          # 使用构建脚本"
