# x-crack Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=x-crack

# Binary names for different platforms
BINARY_DARWIN_AMD64=$(BINARY_NAME)_darwin_amd64
BINARY_DARWIN_ARM64=$(BINARY_NAME)_darwin_arm64
BINARY_LINUX_AMD64=$(BINARY_NAME)_linux_amd64
BINARY_LINUX_ARM64=$(BINARY_NAME)_linux_arm64
BINARY_WINDOWS_AMD64=$(BINARY_NAME)_windows_amd64.exe
BINARY_WINDOWS_ARM64=$(BINARY_NAME)_windows_arm64.exe

# Legacy binary names (for backward compatibility)
BINARY_UNIX=$(BINARY_NAME)_unix
BINARY_LINUX=$(BINARY_NAME)_linux
BINARY_WINDOWS=$(BINARY_NAME).exe
BINARY_DARWIN=$(BINARY_NAME)_darwin

# Version info
VERSION ?= 2.0.0
GIT_COMMIT ?= $(shell git rev-parse --short HEAD)
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d_%H:%M:%S')

# Build flags
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.gitCommit=$(GIT_COMMIT) -X main.buildTime=$(BUILD_TIME)"
BUILD_TAGS=-tags "nocgo"

.PHONY: all build clean test deps help install

all: clean deps build

build:
	@echo "Building x-crack..."
	$(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_NAME) -v

# Cross-platform build targets
build-darwin-amd64:
	@echo "Building for macOS (Intel)..."
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_DARWIN_AMD64) -v

build-darwin-arm64:
	@echo "Building for macOS (Apple Silicon)..."
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_DARWIN_ARM64) -v

build-linux-amd64:
	@echo "Building for Linux (x86_64)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_LINUX_AMD64) -v

build-linux-arm64:
	@echo "Building for Linux (ARM64)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_LINUX_ARM64) -v

build-windows-amd64:
	@echo "Building for Windows (x86_64)..."
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_WINDOWS_AMD64) -v

build-windows-arm64:
	@echo "Building for Windows (ARM64)..."
	CGO_ENABLED=0 GOOS=windows GOARCH=arm64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_WINDOWS_ARM64) -v

# Legacy build targets (for backward compatibility)
build-linux:
	@echo "Building for Linux (legacy)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_LINUX) -v

build-windows:
	@echo "Building for Windows (legacy)..."
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_WINDOWS) -v

build-darwin:
	@echo "Building for macOS (legacy)..."
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_TAGS) $(LDFLAGS) -o $(BINARY_DARWIN) -v

# Build all platforms
build-all: build-darwin-amd64 build-darwin-arm64 build-linux-amd64 build-linux-arm64 build-windows-amd64 build-windows-arm64
	@echo "Built binaries for all platforms"

# Legacy build-all target
build-all-legacy: build-linux build-windows build-darwin
	@echo "Built binaries for all platforms (legacy)"

clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_DARWIN_AMD64)
	rm -f $(BINARY_DARWIN_ARM64)
	rm -f $(BINARY_LINUX_AMD64)
	rm -f $(BINARY_LINUX_ARM64)
	rm -f $(BINARY_WINDOWS_AMD64)
	rm -f $(BINARY_WINDOWS_ARM64)
	rm -f $(BINARY_UNIX)
	rm -f $(BINARY_LINUX)
	rm -f $(BINARY_WINDOWS)
	rm -f $(BINARY_DARWIN)
	rm -f release/*.tar.gz
	rm -rf release/

test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

update-deps:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

install: build
	@echo "Installing x-crack..."
	cp $(BINARY_NAME) /usr/local/bin/

format:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, skipping..."; \
	fi

vet:
	@echo "Running go vet..."
	$(GOCMD) vet ./...

check: format vet lint test
	@echo "All checks passed!"

# Development helpers
dev-setup:
	@echo "Setting up development environment..."
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint@latest

run-example:
	@echo "Running example SSH brute force..."
	./$(BINARY_NAME) -target 127.0.0.1 -protocol ssh -u admin -p password123 -show-failed -timeout 5s

run-help:
	@echo "Showing help..."
	./$(BINARY_NAME) -help

# Docker support
docker-build:
	@echo "Building Docker image..."
	docker build -t x-crack:$(VERSION) .

docker-run:
	@echo "Running Docker container..."
	docker run --rm -it x-crack:$(VERSION)

# Release preparation
release: clean deps test build-all
	@echo "Creating release archive..."
	mkdir -p release
	cp $(BINARY_DARWIN_AMD64) release/
	cp $(BINARY_DARWIN_ARM64) release/
	cp $(BINARY_LINUX_AMD64) release/
	cp $(BINARY_LINUX_ARM64) release/
	cp $(BINARY_WINDOWS_AMD64) release/
	cp $(BINARY_WINDOWS_ARM64) release/
	cp README.md release/
	cp config.yaml release/
	cp -r dict release/
	tar -czf release/x-crack-$(VERSION).tar.gz -C release .
	@echo "Release archive created: release/x-crack-$(VERSION).tar.gz"

# Legacy release (for backward compatibility)
release-legacy: clean deps test build-all-legacy
	@echo "Creating legacy release archive..."
	mkdir -p release-legacy
	cp $(BINARY_LINUX) release-legacy/
	cp $(BINARY_WINDOWS) release-legacy/
	cp $(BINARY_DARWIN) release-legacy/
	cp README.md release-legacy/
	cp config.yaml release-legacy/
	cp -r dict release-legacy/
	tar -czf release-legacy/x-crack-$(VERSION)-legacy.tar.gz -C release-legacy .
	@echo "Legacy release archive created: release-legacy/x-crack-$(VERSION)-legacy.tar.gz"

# Dictionary management
dict-update:
	@echo "Updating dictionaries..."
	@if [ -d "dict" ]; then \
		echo "Dictionary directory exists"; \
	else \
		mkdir -p dict; \
	fi
	@echo "Dictionary update completed"

help:
	@echo "Available targets:"
	@echo ""
	@echo "Build targets:"
	@echo "  build                - Build the binary for current platform"
	@echo "  build-all            - Build binaries for all platforms (6 platforms)"
	@echo "  build-all-legacy     - Build binaries for legacy platforms (3 platforms)"
	@echo ""
	@echo "Platform-specific builds:"
	@echo "  build-darwin-amd64   - Build for macOS (Intel)"
	@echo "  build-darwin-arm64   - Build for macOS (Apple Silicon)"
	@echo "  build-linux-amd64    - Build for Linux (x86_64)"
	@echo "  build-linux-arm64    - Build for Linux (ARM64)"
	@echo "  build-windows-amd64  - Build for Windows (x86_64)"
	@echo "  build-windows-arm64  - Build for Windows (ARM64)"
	@echo ""
	@echo "Legacy platform builds:"
	@echo "  build-linux          - Build for Linux (legacy)"
	@echo "  build-windows        - Build for Windows (legacy)"
	@echo "  build-darwin         - Build for macOS (legacy)"
	@echo ""
	@echo "Development targets:"
	@echo "  clean                - Clean build artifacts"
	@echo "  test                 - Run tests"
	@echo "  test-coverage        - Run tests with coverage"
	@echo "  deps                 - Download dependencies"
	@echo "  update-deps          - Update dependencies"
	@echo "  install              - Install binary to /usr/local/bin"
	@echo "  format               - Format code"
	@echo "  lint                 - Run linter"
	@echo "  vet                  - Run go vet"
	@echo "  check                - Run all checks (format, vet, lint, test)"
	@echo "  dev-setup            - Setup development environment"
	@echo ""
	@echo "Utility targets:"
	@echo "  run-example          - Run example command"
	@echo "  run-help             - Show help"
	@echo "  docker-build         - Build Docker image"
	@echo "  docker-run           - Run Docker container"
	@echo "  release              - Create release package (all platforms)"
	@echo "  release-legacy       - Create legacy release package"
	@echo "  dict-update          - Update dictionaries"
	@echo "  help                 - Show this help"
