# x-crack Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=x-crack
BINARY_UNIX=$(BINARY_NAME)_unix
BINARY_LINUX=$(BINARY_NAME)_linux
BINARY_WINDOWS=$(BINARY_NAME).exe
BINARY_DARWIN=$(BINARY_NAME)_darwin

# Version info
VERSION ?= 2.0.0
GIT_COMMIT ?= $(shell git rev-parse --short HEAD)
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d_%H:%M:%S')

# Build flags
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.gitCommit=$(GIT_COMMIT) -X main.buildTime=$(BUILD_TIME)"

.PHONY: all build clean test deps help install

all: clean deps build

build:
	@echo "Building x-crack..."
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) -v

build-linux:
	@echo "Building for Linux..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_LINUX) -v

build-windows:
	@echo "Building for Windows..."
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_WINDOWS) -v

build-darwin:
	@echo "Building for macOS..."
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_DARWIN) -v

build-all: build-linux build-windows build-darwin
	@echo "Built binaries for all platforms"

clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)
	rm -f $(BINARY_LINUX)
	rm -f $(BINARY_WINDOWS)
	rm -f $(BINARY_DARWIN)

test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

update-deps:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

install: build
	@echo "Installing x-crack..."
	cp $(BINARY_NAME) /usr/local/bin/

format:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, skipping..."; \
	fi

vet:
	@echo "Running go vet..."
	$(GOCMD) vet ./...

check: format vet lint test
	@echo "All checks passed!"

# Development helpers
dev-setup:
	@echo "Setting up development environment..."
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint@latest

run-example:
	@echo "Running example SSH brute force..."
	./$(BINARY_NAME) -target 127.0.0.1 -protocol ssh -u admin -p password123 -show-failed -timeout 5s

run-help:
	@echo "Showing help..."
	./$(BINARY_NAME) -help

# Docker support
docker-build:
	@echo "Building Docker image..."
	docker build -t x-crack:$(VERSION) .

docker-run:
	@echo "Running Docker container..."
	docker run --rm -it x-crack:$(VERSION)

# Release preparation
release: clean deps test build-all
	@echo "Creating release archive..."
	mkdir -p release
	cp $(BINARY_LINUX) release/
	cp $(BINARY_WINDOWS) release/
	cp $(BINARY_DARWIN) release/
	cp README.md release/
	cp config.yaml release/
	cp -r dict release/
	tar -czf release/x-crack-$(VERSION).tar.gz -C release .
	@echo "Release archive created: release/x-crack-$(VERSION).tar.gz"

# Dictionary management
dict-update:
	@echo "Updating dictionaries..."
	@if [ -d "dict" ]; then \
		echo "Dictionary directory exists"; \
	else \
		mkdir -p dict; \
	fi
	@echo "Dictionary update completed"

help:
	@echo "Available targets:"
	@echo "  build          - Build the binary"
	@echo "  build-all      - Build binaries for all platforms"
	@echo "  build-linux    - Build for Linux"
	@echo "  build-windows  - Build for Windows"
	@echo "  build-darwin   - Build for macOS"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  deps           - Download dependencies"
	@echo "  update-deps    - Update dependencies"
	@echo "  install        - Install binary to /usr/local/bin"
	@echo "  format         - Format code"
	@echo "  lint           - Run linter"
	@echo "  vet            - Run go vet"
	@echo "  check          - Run all checks (format, vet, lint, test)"
	@echo "  dev-setup      - Setup development environment"
	@echo "  run-example    - Run example command"
	@echo "  run-help       - Show help"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  release        - Create release package"
	@echo "  dict-update    - Update dictionaries"
	@echo "  help           - Show this help"
