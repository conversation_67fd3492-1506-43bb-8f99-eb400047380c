package main

import (
	"bufio"
	"context"
	"encoding/csv"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/XTeam-Wing/x-crack/pkg/brute"
	"github.com/XTeam-Wing/x-crack/pkg/protocols"
	"github.com/XTeam-Wing/x-crack/pkg/utils"
	"github.com/samber/lo"
)

const (
	version = "2.0.0"
	banner  = `
__  __       ____                 _
\ \/ /      / ___|_ __ __ _  ___| | __
 \  /      | |   | '__/ _` + "`" + ` |/ __| |/ /
 /  \      | |___| | | (_| | (__|   <
/_/\_\      \____|_|  \__,_|\___|_|\_\

    Service Weak Password Brute Force Tool v%s

`
)

// Options CLI选项结构
type Options struct {
	// 目标设置
	Targets    string
	TargetFile string
	Protocol   string

	// 认证设置
	Usernames    string
	UserFile     string
	Passwords    string
	PassFile     string
	UserPassFile string

	// 爆破设置
	TargetConcurrent int
	TaskConcurrent   int
	Delay            string
	Timeout          string
	Retries          int
	OkToStop         bool

	// 输出设置
	Output       string
	Format       string
	Verbose      bool
	Debug        bool
	Silent       bool
	NoColor      bool
	ShowFailed   bool
	ShowProgress bool

	// 其他设置
	Config  string
	Version bool
	Help    bool
}

// ResultRecord 结果记录
type ResultRecord struct {
	Protocol     string    `json:"protocol" csv:"protocol"`
	Target       string    `json:"target" csv:"target"`
	Port         int       `json:"port" csv:"port"`
	Username     string    `json:"username" csv:"username"`
	Password     string    `json:"password" csv:"password"`
	Success      bool      `json:"success" csv:"success"`
	Banner       string    `json:"banner,omitempty" csv:"banner"`
	Error        string    `json:"error,omitempty" csv:"error"`
	ResponseTime string    `json:"response_time" csv:"response_time"`
	Timestamp    time.Time `json:"timestamp" csv:"timestamp"`
}

var (
	options       = &Options{}
	resultRecords []ResultRecord
	outputFile    *os.File
	csvWriter     *csv.Writer
)

func main() {
	// 解析命令行参数
	parseFlags()

	// 处理版本和帮助
	if options.Version {
		fmt.Printf("x-crack version %s\n", version)
		return
	}

	if options.Help {
		printUsage()
		return
	}

	// 配置日志级别
	configureLogging()

	// 注册所有协议处理器
	protocols.RegisterAllProtocols()

	// 验证参数
	if err := validateOptions(); err != nil {
		fmt.Printf("[FATAL] Invalid options: %v\n", err)
		os.Exit(1)
	}

	// 创建上下文和信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		fmt.Println("[INFO] Received interrupt signal, stopping...")
		cancel()
	}()

	// 准备输出文件
	if err := prepareOutput(); err != nil {
		fmt.Printf("[FATAL] Failed to prepare output: %v\n", err)
		os.Exit(1)
	}
	defer closeOutput()

	// 构建目标列表
	targets, err := buildTargets()
	if err != nil {
		fmt.Printf("[FATAL] Failed to build targets: %v\n", err)
		os.Exit(1)
	}

	if len(targets) == 0 {
		fmt.Println("[FATAL] No targets specified")
		os.Exit(1)
	}

	// 构建认证字典
	userDict, passDict, err := buildCredentials()
	if err != nil {
		fmt.Printf("[FATAL] Failed to build credentials: %v\n", err)
		os.Exit(1)
	}

	if len(userDict) == 0 {
		fmt.Println("[WARNING] No usernames specified, using common usernames")
		userDict = utils.GenerateCommonUsernames()
	}

	if len(passDict) == 0 {
		fmt.Println("[WARNING] No passwords specified, using common passwords")
		passDict = utils.GenerateCommonPasswords()
	}

	fmt.Printf("[INFO] Loaded %d targets, %d usernames, %d passwords\n",
		len(targets), len(userDict), len(passDict))

	// 创建配置
	config := createConfig(userDict, passDict)

	// 创建结果回调
	resultCallback := createResultCallback()

	// 执行爆破
	fmt.Println("[INFO] Starting brute force attack...")
	if err := brute.BatchBruteWithConfig(ctx, targets, userDict, passDict, resultCallback, config); err != nil {
		fmt.Printf("[ERROR] Brute force failed: %v\n", err)
	}

	// 输出最终统计
	printFinalStats()
}

// parseFlags 解析命令行参数
func parseFlags() {
	// 目标设置
	flag.StringVar(&options.Targets, "t", "", "目标列表，支持IP:port格式 (例如: ***********:22,***********:3389)")
	flag.StringVar(&options.Targets, "targets", "", "目标列表，支持IP:port格式 (例如: ***********:22,***********:3389)")
	flag.StringVar(&options.TargetFile, "tf", "", "包含目标的文件，每行一个IP:port")
	flag.StringVar(&options.TargetFile, "target-file", "", "包含目标的文件，每行一个IP:port")
	flag.StringVar(&options.Protocol, "protocol", "", "使用的协议")

	// 认证设置
	flag.StringVar(&options.Usernames, "u", "", "用户名列表 (逗号分隔)")
	flag.StringVar(&options.Usernames, "usernames", "", "用户名列表 (逗号分隔)")
	flag.StringVar(&options.UserFile, "uf", "", "包含用户名的文件")
	flag.StringVar(&options.UserFile, "user-file", "", "包含用户名的文件")
	flag.StringVar(&options.Passwords, "p", "", "密码列表 (逗号分隔)")
	flag.StringVar(&options.Passwords, "passwords", "", "密码列表 (逗号分隔)")
	flag.StringVar(&options.PassFile, "pf", "", "包含密码的文件")
	flag.StringVar(&options.PassFile, "pass-file", "", "包含密码的文件")
	flag.StringVar(&options.UserPassFile, "userpass-file", "", "包含用户名:密码组合的文件")

	// 爆破设置
	flag.IntVar(&options.TargetConcurrent, "target-concurrent", 10, "全局最大并发数 (默认: 10, 推荐: 5-20)")
	flag.IntVar(&options.TaskConcurrent, "task-concurrent", 5, "单目标最大并发数 (默认: 5, 推荐: 3-10)")
	flag.StringVar(&options.Delay, "delay", "100ms", "请求间最小延迟 (默认: 100ms, 推荐: 100ms-1s)")
	flag.StringVar(&options.Timeout, "timeout", "5s", "每个请求的超时时间 (默认: 5s)")
	flag.IntVar(&options.Retries, "retries", 1, "失败重试次数 (默认: 1, 推荐: 1-3)")
	flag.BoolVar(&options.OkToStop, "ok-to-stop", false, "首次成功认证后停止 (默认: false)")

	// 输出设置
	flag.StringVar(&options.Output, "output", "", "输出文件路径")
	flag.StringVar(&options.Format, "format", "text", "输出格式 (text,json,csv) (默认: text)")
	flag.BoolVar(&options.Verbose, "v", false, "详细输出")
	flag.BoolVar(&options.Verbose, "verbose", false, "详细输出")
	flag.BoolVar(&options.Debug, "d", false, "调试模式")
	flag.BoolVar(&options.Debug, "debug", false, "调试模式")
	flag.BoolVar(&options.Silent, "silent", false, "静默模式")
	flag.BoolVar(&options.NoColor, "no-color", false, "禁用彩色输出")
	flag.BoolVar(&options.ShowFailed, "show-failed", false, "显示失败的认证尝试")
	flag.BoolVar(&options.ShowProgress, "show-progress", true, "显示进度条 (默认: true)")

	// 其他设置
	flag.StringVar(&options.Config, "config", "", "配置文件路径")
	flag.BoolVar(&options.Version, "version", false, "显示版本信息")
	flag.BoolVar(&options.Help, "help", false, "显示帮助信息")

	flag.Parse()
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Printf(`Usage:
  %s [flags]

目标设置:
   -t, -targets string      目标列表，支持IP:port格式 (例如: ***********:22,***********:3389)
   -tf, -target-file string 包含目标的文件，每行一个IP:port
   -protocol string         使用的协议

认证设置:
   -u, -usernames string   用户名列表 (逗号分隔)
   -uf, -user-file string  包含用户名的文件
   -p, -passwords string   密码列表 (逗号分隔)
   -pf, -pass-file string  包含密码的文件
   -userpass-file string   包含用户名:密码组合的文件

爆破设置:
   -target-concurrent int  全局最大并发数 (默认: 10, 推荐: 5-20)
   -task-concurrent int    单目标最大并发数 (默认: 5, 推荐: 3-10)
   -delay string           请求间最小延迟 (默认: 200ms, 推荐: 100ms-1s)
   -timeout string         每个请求的超时时间 (默认: 10s)
   -retries int            失败重试次数 (默认: 2, 推荐: 1-3)
   -ok-to-stop             首次成功认证后停止 (默认: false)

输出设置:
   -output string  输出文件路径
   -format string  输出格式 (text,json,csv) (默认: text)
   -v, -verbose    详细输出
   -d, -debug      调试模式
   -silent         静默模式
   -no-color       禁用彩色输出
   -show-failed    显示失败的认证尝试
   -show-progress  显示进度条 (默认: true)

其他设置:
   -config string  配置文件路径
   -version        显示版本信息
   -help           显示帮助信息

示例:
   # SSH爆破单个目标
   %s -targets ***********00:22 -protocol ssh -u admin -p password123

   # 爆破多个目标
   %s -targets ***********:22,***********:3389 -protocol ssh -uf users.txt -pf passwords.txt

   # 从文件读取目标
   %s -target-file targets.txt -protocol ssh -u admin,root -p 123456,password

   # 输出结果到JSON文件
   %s -targets ***********00:3306 -protocol mysql -u root -p 123456,password -output results.json -format json
`, os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0])
}

// configureLogging 配置日志
func configureLogging() {
	// Console output configuration
	// Log levels can be controlled by flags if needed
	if options.Silent {
		// Silent mode - minimal output
	} else if options.Debug {
		// Debug mode - verbose output
		fmt.Println("[DEBUG] Debug mode enabled")
	} else if options.Verbose {
		// Verbose mode
		fmt.Println("[VERBOSE] Verbose mode enabled")
	}
}

// validateOptions 验证选项
func validateOptions() error {
	// 添加调试信息
	if options.Debug {
		fmt.Printf("[DEBUG] 解析后的参数值:\n")
		fmt.Printf("  Targets: %q\n", options.Targets)
		fmt.Printf("  TargetFile: %q\n", options.TargetFile)
		fmt.Printf("  Protocol: %q\n", options.Protocol)
	}

	// 检查目标参数
	if options.Targets == "" && options.TargetFile == "" {
		return fmt.Errorf("必须指定目标: -targets 或 -target-file")
	}

	// 检查协议参数
	if options.Protocol == "" {
		return fmt.Errorf("必须指定协议: -protocol")
	}

	// 验证输出格式
	if options.Format != "text" && options.Format != "json" && options.Format != "csv" {
		return fmt.Errorf("无效的输出格式: %s (支持: text, json, csv)", options.Format)
	}

	// 验证并发参数
	if options.TargetConcurrent <= 0 {
		return fmt.Errorf("target-concurrent 必须大于 0")
	}
	if options.TaskConcurrent <= 0 {
		return fmt.Errorf("task-concurrent 必须大于 0")
	}

	return nil
}

// buildTargets 构建目标列表
func buildTargets() ([]brute.Target, error) {
	var targets []brute.Target
	var targetSpecs []string

	// 收集目标列表 (IP:port格式)
	if options.Targets != "" {
		targetList := strings.Split(options.Targets, ",")
		for _, target := range targetList {
			target = strings.TrimSpace(target)
			if target != "" {
				targetSpecs = append(targetSpecs, target)
			}
		}
	}

	// 从文件加载目标
	if options.TargetFile != "" {
		fileTargets, err := utils.LoadLinesFromFile(options.TargetFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load targets from file: %w", err)
		}
		targetSpecs = append(targetSpecs, fileTargets...)
	}

	// 去重目标
	targetSpecs = lo.Uniq(targetSpecs)

	// 解析目标规格并生成目标
	for _, targetSpec := range targetSpecs {
		host, port, err := parseTargetSpec(targetSpec)
		if err != nil {
			if !options.Silent {
				fmt.Printf("[WARNING] Failed to parse target '%s': %v\n", targetSpec, err)
			}
			continue
		}

		// 如果没有指定端口，使用协议默认端口
		targetPorts := []int{port}
		if port == 0 {
			targetPorts = utils.GetDefaultPorts(options.Protocol)
		}

		for _, targetPort := range targetPorts {
			targets = append(targets, brute.Target{
				Type: options.Protocol,
				Host: host,
				Port: targetPort,
			})
		}
	}

	return targets, nil
}

// parseTargetSpec 解析目标规格 (IP:port 或 IP)
func parseTargetSpec(targetSpec string) (host string, port int, err error) {
	// 检查是否包含端口
	if strings.Contains(targetSpec, ":") {
		parts := strings.Split(targetSpec, ":")
		if len(parts) != 2 {
			return "", 0, fmt.Errorf("invalid target format: %s (expected IP:port)", targetSpec)
		}

		host = strings.TrimSpace(parts[0])
		portStr := strings.TrimSpace(parts[1])

		if host == "" {
			return "", 0, fmt.Errorf("empty host in target: %s", targetSpec)
		}

		port, err = strconv.Atoi(portStr)
		if err != nil {
			return "", 0, fmt.Errorf("invalid port in target %s: %v", targetSpec, err)
		}

		if port <= 0 || port > 65535 {
			return "", 0, fmt.Errorf("port out of range in target %s: %d", targetSpec, port)
		}

		return host, port, nil
	}

	// 只有IP，没有端口
	host = strings.TrimSpace(targetSpec)
	if host == "" {
		return "", 0, fmt.Errorf("empty host: %s", targetSpec)
	}

	return host, 0, nil
}

// buildCredentials 构建认证字典
func buildCredentials() ([]string, []string, error) {
	var userDict []string
	var passDict []string

	// 收集用户名
	if options.Usernames != "" {
		userList := strings.Split(options.Usernames, ",")
		for _, user := range userList {
			user = strings.TrimSpace(user)
			if user != "" {
				userDict = append(userDict, user)
			}
		}
	}

	// 从文件加载用户名
	if options.UserFile != "" {
		fileUsers, err := utils.LoadLinesFromFile(options.UserFile)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load usernames from file: %w", err)
		}
		userDict = append(userDict, fileUsers...)
	}

	// 收集密码
	if options.Passwords != "" {
		passList := strings.Split(options.Passwords, ",")
		for _, pass := range passList {
			pass = strings.TrimSpace(pass)
			passDict = append(passDict, pass)
		}
	}

	// 从文件加载密码
	if options.PassFile != "" {
		filePasswords, err := utils.LoadLinesFromFile(options.PassFile)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load passwords from file: %w", err)
		}
		passDict = append(passDict, filePasswords...)
	}

	// 从用户密码组合文件加载
	if options.UserPassFile != "" {
		combos, err := loadUserPassFile(options.UserPassFile)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to load user:pass combinations: %w", err)
		}
		for _, combo := range combos {
			parts := strings.SplitN(combo, ":", 2)
			if len(parts) == 2 {
				userDict = append(userDict, parts[0])
				passDict = append(passDict, parts[1])
			}
		}
	}

	// 去重
	userDict = lo.Uniq(userDict)
	passDict = lo.Uniq(passDict)

	return userDict, passDict, nil
}

// loadUserPassFile 加载用户名:密码组合文件
func loadUserPassFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var combos []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") && strings.Contains(line, ":") {
			combos = append(combos, line)
		}
	}

	return combos, scanner.Err()
}

// createConfig 创建配置
func createConfig(userDict, passDict []string) *brute.Config {
	config := brute.DefaultConfig()

	// 设置并发
	config.TargetConcurrent = options.TargetConcurrent
	config.TaskConcurrent = options.TaskConcurrent

	// 设置延迟
	if delay, err := time.ParseDuration(options.Delay); err == nil {
		config.MinDelay = delay
	}

	// 设置超时
	if timeout, err := time.ParseDuration(options.Timeout); err == nil {
		config.Timeout = timeout
	}

	// 设置重试
	config.MaxRetries = options.Retries

	// 设置停止条件
	config.OkToStop = options.OkToStop

	// 设置进度显示
	config.ShowProgress = options.ShowProgress && !options.Silent

	// 设置字典
	config.UserDict = userDict
	config.PassDict = passDict

	return config
}

// prepareOutput 准备输出
func prepareOutput() error {
	if options.Output == "" {
		return nil
	}

	// 创建输出目录
	dir := filepath.Dir(options.Output)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// 打开输出文件
	var err error
	outputFile, err = os.Create(options.Output)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}

	// 初始化CSV writer
	if options.Format == "csv" {
		csvWriter = csv.NewWriter(outputFile)
		// 写入CSV头部
		headers := []string{"protocol", "target", "port", "username", "password", "success", "banner", "error", "response_time", "timestamp"}
		if err := csvWriter.Write(headers); err != nil {
			return fmt.Errorf("failed to write CSV headers: %w", err)
		}
		csvWriter.Flush()
	}

	return nil
}

// closeOutput 关闭输出
func closeOutput() {
	if csvWriter != nil {
		csvWriter.Flush()
	}
	if outputFile != nil {
		// 写入JSON格式的结果
		if options.Format == "json" {
			encoder := json.NewEncoder(outputFile)
			encoder.SetIndent("", "  ")
			encoder.Encode(resultRecords)
		}
		outputFile.Close()
	}
}

// createResultCallback 创建结果回调
func createResultCallback() brute.ResultCallback {
	return func(result *brute.BruteResult) {
		record := ResultRecord{
			Protocol:     result.Item.Type,
			Target:       result.Item.Target,
			Port:         result.Item.Port,
			Username:     result.Item.Username,
			Password:     result.Item.Password,
			Success:      result.Success,
			Banner:       result.Banner,
			ResponseTime: result.ResponseTime.String(),
			Timestamp:    time.Now(),
		}

		if result.Error != nil {
			record.Error = result.Error.Error()
		}

		// 添加到记录列表
		resultRecords = append(resultRecords, record)

		// 控制台输出
		if result.Success {
			if !options.Silent {
				fmt.Printf("[SUCCESS] %s\n", result.String())
				if result.Banner != "" {
					fmt.Printf("  └─ %s\n", result.Banner)
				}
			}
		} else {
			if options.ShowFailed && !options.Silent {
				fmt.Printf("[FAILED] %s\n", result.String())
				if result.Error != nil && options.Debug {
					fmt.Printf("  └─ %v\n", result.Error)
				}
			}
		}

		// 文件输出
		if options.Output != "" {
			switch options.Format {
			case "text":
				writeTextOutput(record)
			case "csv":
				writeCSVOutput(record)
			case "json":
				// JSON在closeOutput中批量写入
			}
		}
	}
}

// writeTextOutput 写入文本格式输出
func writeTextOutput(record ResultRecord) {
	if outputFile == nil {
		return
	}

	status := "FAILED"
	if record.Success {
		status = "SUCCESS"
	}

	line := fmt.Sprintf("[%s] %s://%s:%s@%s:%d - %s\n",
		status, record.Protocol, record.Username, record.Password,
		record.Target, record.Port, record.Timestamp.Format("2006-01-02 15:04:05"))

	if record.Banner != "" {
		line += fmt.Sprintf("  Banner: %s\n", record.Banner)
	}
	if record.Error != "" {
		line += fmt.Sprintf("  Error: %s\n", record.Error)
	}

	outputFile.WriteString(line)
}

// writeCSVOutput 写入CSV格式输出
func writeCSVOutput(record ResultRecord) {
	if csvWriter == nil {
		return
	}

	row := []string{
		record.Protocol,
		record.Target,
		strconv.Itoa(record.Port),
		record.Username,
		record.Password,
		strconv.FormatBool(record.Success),
		record.Banner,
		record.Error,
		record.ResponseTime,
		record.Timestamp.Format("2006-01-02 15:04:05"),
	}

	csvWriter.Write(row)
	csvWriter.Flush()
}

// printFinalStats 打印最终统计
func printFinalStats() {
	if options.Silent {
		return
	}

	total := len(resultRecords)
	success := 0
	failed := 0

	for _, record := range resultRecords {
		if record.Success {
			success++
		} else {
			failed++
		}
	}

	if !options.Silent {
		fmt.Println("=== Final Statistics ===")
		fmt.Printf("Total Attempts: %d\n", total)
		fmt.Printf("Successful: %d\n", success)
		fmt.Printf("Failed: %d\n", failed)

		if total > 0 {
			successRate := float64(success) / float64(total) * 100
			fmt.Printf("Success Rate: %.2f%%\n", successRate)
		}
	}

	if options.Output != "" && !options.Silent {
		fmt.Printf("Results saved to: %s\n", options.Output)
	}

	// 显示成功的认证
	if success > 0 && !options.Silent {
		fmt.Println("=== Successful Authentications ===")
		for _, record := range resultRecords {
			if record.Success {
				fmt.Printf("%s://%s:%s@%s:%d\n",
					record.Protocol, record.Username, record.Password,
					record.Target, record.Port)
			}
		}
	}
}
