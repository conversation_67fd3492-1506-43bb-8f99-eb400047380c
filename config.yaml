# x-crack config file
# generated by https://github.com/projectdiscovery/goflags

# target host (e.g. ***********)
#target: 

# target hosts (comma separated)
#targets: []

# file containing target hosts
#target-file: 

# target port
#port: 0

# port range (e.g. 22,3389,1433-1434)
#ports: 

# file containing ports
#port-file: 

# protocol to use (ssh,mysql,ftp,etc.)
#protocol: 

# protocols to use (comma separated)
#protocols: []

# username for authentication
#username: 

# usernames (comma separated)
#usernames: []

# file containing usernames
#user-file: 

# password for authentication
#password: 

# passwords (comma separated)
#passwords: []

# file containing passwords
#pass-file: 

# file containing username:password combinations
#userpass-file: 

# number of concurrent targets
#target-concurrent: 50

# number of concurrent tasks per target
#task-concurrent: 1

# delay between requests (e.g. 100ms)
#delay: 

# timeout for each request
#timeout: 10s

# number of retries for failed requests
#retries: 3

# stop after first successful authentication
#ok-to-stop: false

# output file path
#output: 

# output format (text,json,csv)
#format: text

# verbose output
#verbose: false

# silent mode
#silent: false

# disable colored output
#no-color: false

# show failed authentication attempts
#show-failed: false

# configuration file path
#config: 

# show version information
#version: false

# show help message