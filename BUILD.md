# x-crack 跨平台编译指南

本文档说明如何为 x-crack 项目进行跨平台编译，生成多个目标平台的二进制文件。

## 支持的目标平台

项目已配置支持以下6个目标平台：

| 平台 | 架构 | 说明 |
|------|------|------|
| darwin | amd64 | macOS Intel |
| darwin | arm64 | macOS Apple Silicon |
| linux | amd64 | Linux x86_64 |
| linux | arm64 | Linux ARM64 |
| windows | amd64 | Windows x86_64 |
| windows | arm64 | Windows ARM64 |

## 构建方法

### 方法1：使用 Makefile

#### 单平台构建
```bash
# 构建特定平台
make build-darwin-amd64   # macOS Intel
make build-darwin-arm64   # macOS Apple Silicon
make build-linux-amd64    # Linux x86_64
make build-linux-arm64    # Linux ARM64
make build-windows-amd64  # Windows x86_64
make build-windows-arm64  # Windows ARM64
```

#### 批量构建
```bash
# 构建所有6个平台
make build-all

# 构建传统3个平台（向后兼容）
make build-all-legacy

# 清理构建产物
make clean
```

#### 发布管理
```bash
# 创建包含所有平台的发布包
make release

# 查看所有可用目标
make help
```

### 方法2：使用构建脚本

#### 基本用法
```bash
# 构建所有平台
./build.sh --all

# 构建指定平台
./build.sh darwin/amd64 linux/amd64

# 清理后构建
./build.sh --clean --all

# 创建发布包
./build.sh --release

# 查看帮助
./build.sh --help
```

#### 高级选项
```bash
# 详细输出
./build.sh --verbose --all

# 组合使用
./build.sh --clean --all --release
```

## 生成的文件

构建完成后，会在项目根目录生成以下二进制文件：

```
x-crack_darwin_amd64      # macOS Intel
x-crack_darwin_arm64      # macOS Apple Silicon  
x-crack_linux_amd64       # Linux x86_64
x-crack_linux_arm64       # Linux ARM64
x-crack_windows_amd64.exe # Windows x86_64
x-crack_windows_arm64.exe # Windows ARM64
```

## 发布包结构

使用 `make release` 或 `./build.sh --release` 创建的发布包包含：

```
release/
├── x-crack_darwin_amd64
├── x-crack_darwin_arm64
├── x-crack_linux_amd64
├── x-crack_linux_arm64
├── x-crack_windows_amd64.exe
├── x-crack_windows_arm64.exe
├── README.md
├── config.yaml
└── dict/
    ├── usernames.txt
    ├── passwords.txt
    └── combo.txt
```

## 构建配置

### 编译标志
- `CGO_ENABLED=0`: 禁用CGO，确保静态链接
- `-tags "nocgo"`: 使用构建标签排除CGO依赖
- `-ldflags`: 注入版本信息

### 版本信息
构建时会自动注入以下信息：
- 版本号：从 `VERSION` 环境变量或默认 `2.0.0`
- Git提交：自动获取当前提交哈希
- 构建时间：UTC时间戳

## 故障排除

### CGO依赖问题
如果遇到CGO相关的编译错误：

1. **确保CGO被禁用**：
   ```bash
   export CGO_ENABLED=0
   ```

2. **使用构建标签**：
   ```bash
   go build -tags "nocgo" -o binary_name
   ```

3. **检查依赖**：
   ```bash
   go mod tidy
   go mod download
   ```

### 平台特定问题

#### macOS
- 确保安装了 Xcode Command Line Tools
- 对于Apple Silicon，使用 `darwin/arm64`

#### Linux
- ARM64构建需要交叉编译工具链
- 建议在目标平台上进行最终测试

#### Windows
- 二进制文件会自动添加 `.exe` 扩展名
- 建议在Windows上测试运行

## 环境变量

可以通过环境变量自定义构建：

```bash
# 设置版本号
export VERSION="2.1.0"

# 设置Git提交（通常自动获取）
export GIT_COMMIT="abc1234"

# 设置构建时间（通常自动生成）
export BUILD_TIME="2025-01-01_12:00:00"
```

## Docker构建（推荐）

对于复杂的依赖问题，建议使用Docker：

```bash
# 构建Docker镜像
make docker-build

# 运行Docker容器
make docker-run
```

## 最佳实践

1. **在目标平台测试**：跨平台编译的二进制文件应在目标平台上进行测试
2. **静态链接**：使用 `CGO_ENABLED=0` 确保二进制文件的可移植性
3. **版本管理**：为发布版本设置正确的版本号
4. **自动化**：在CI/CD中使用这些构建脚本

## 示例工作流

```bash
# 1. 清理环境
make clean

# 2. 更新依赖
go mod tidy

# 3. 构建所有平台
make build-all

# 4. 创建发布包
make release

# 5. 验证构建结果
ls -la release/
```

## 支持与反馈

如果在跨平台编译过程中遇到问题：

1. 检查Go版本兼容性
2. 确认目标平台支持
3. 查看构建日志中的错误信息
4. 尝试在目标平台上直接构建

---

**注意**：本项目的跨平台编译配置已经过优化，支持主流的操作系统和架构组合。如需支持其他平台，请修改Makefile和构建脚本中的平台列表。
